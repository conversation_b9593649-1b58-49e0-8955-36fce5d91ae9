<div class="modal fade" id="reschedule-only" tabindex="-1" aria-labelledby="rescheduleOnlyLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-5">
                <p class="modal-title fs-15 sora black semi_bold" id="rescheduleOnlyLabel">Reschedule Booking</p>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form class="form-reschedule-only">
                    <input type="hidden" name="service_id" id="ro_service_id" value="">
                    <input type="hidden" name="booking_id" id="ro_booking_id" value="">
                    <input type="hidden" name="original_booking_date" id="ro_original_date" value="">
                    <input type="hidden" name="original_booking_time" id="ro_original_time" value="">
                    <input type="hidden" name="booking_date" id="ro_booking_date" value="">
                    <input type="hidden" name="booking_time" id="ro_booking_time" value="">

                    <div class="calendar-container">
                        <div class="title">Select Date & Time</div>

                        <div class="date-header">
                            <select class="month-select" id="ro_monthSelect"></select>
                            <div class="nav-buttons">
                                <button type="button" class="nav-btn" id="ro_prevBtn">‹</button>
                                <button type="button" class="nav-btn" id="ro_nextBtn">›</button>
                            </div>
                        </div>

                        <div class="date-grid" id="ro_dateGrid"></div>

                        <div class="time-section-title mt-8 mb-3">Available Time Slots</div>
                        <div class="time-grid mb-8" id="ro_timeGrid"></div>

                        <div class="selection-display" id="ro_selectionDisplay">
                            <div class="no-selection">Select a date and time</div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="cancel-btn" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="save-btn" id="ro_save_btn">Reschedule</button>
            </div>
        </div>
    </div>
</div>

@push('js')
<script>
(function(){
    const months = ["January","February","March","April","May","June","July","August","September","October","November","December"];
    const daysShort = ["Sun","Mon","Tue","Wed","Thu","Fri","Sat"];
    let currentWeekStart = new Date();
    let selectedDate = null; // Date obj
    let selectedTime = null; // HH:MM
    let cachedSlots = null; // last fetched slot definition

    function pad(n){ return String(n).padStart(2,'0'); }

    function buildMonthOptions(){
        const sel = document.getElementById('ro_monthSelect');
        if (!sel) return;
        sel.innerHTML = '';
        for (let i=0;i<12;i++){
            const d = new Date(); d.setMonth(d.getMonth()+i);
            const opt = document.createElement('option');
            opt.value = d.getFullYear()+'-'+d.getMonth();
            opt.textContent = months[d.getMonth()]+' '+d.getFullYear();
            if (i===0) opt.selected = true;
            sel.appendChild(opt);
        }
    }

    function setToStartOfWeek(d){ const dt = new Date(d); dt.setDate(dt.getDate()-dt.getDay()); return dt; }

    function renderDates(disabledDates = []){
        const grid = document.getElementById('ro_dateGrid');
        if (!grid) return; grid.innerHTML='';
        for (let i=0;i<7;i++){
            const d = new Date(currentWeekStart); d.setDate(currentWeekStart.getDate()+i);
            const div = document.createElement('div');
            div.className='date-item';
            div.setAttribute('data-date', d.toDateString());
            const today = new Date(); today.setHours(0,0,0,0);
            const isPast = d < today;
            const isDisabled = isPast || disabledDates.includes(d.toISOString().split('T')[0]);
            if (selectedDate && d.toDateString() === selectedDate.toDateString()) div.classList.add('selected');
            if (isDisabled){
                div.classList.add('disabled');
            } else {
                div.addEventListener('click', function(){ selectDate(this, d); });
            }
            div.innerHTML = '<div class="date-number">'+d.getDate()+'</div><div class="date-day">'+daysShort[d.getDay()]+'</div>';
            grid.appendChild(div);
        }
        updateMonthSelect();
    }

    function updateMonthSelect(){
        const sel = document.getElementById('ro_monthSelect'); if (!sel) return;
        const mid = new Date(currentWeekStart); mid.setDate(mid.getDate()+3);
        const target = mid.getFullYear()+"-"+mid.getMonth();
        for (let i=0;i<sel.options.length;i++){ if (sel.options[i].value===target){ sel.selectedIndex=i; break; } }
    }

    function updateDisplay(){
        const el = document.getElementById('ro_selectionDisplay'); if (!el) return;
        if (selectedDate && selectedTime){
            const opts = {weekday:'long', year:'numeric', month:'long', day:'numeric'};
            el.innerHTML = '<strong>Selected:</strong> '+ selectedDate.toLocaleDateString('en-US', opts) +' at '+ selectedTime;
        } else if (selectedDate){
            const opts = {weekday:'long', year:'numeric', month:'long', day:'numeric'};
            el.innerHTML = '<strong>Selected:</strong> '+ selectedDate.toLocaleDateString('en-US', opts) +' - Please select a time';
        } else {
            el.innerHTML = '<div class="no-selection">Select a date and time</div>';
        }
    }

    function selectDate(el, d){
        document.querySelectorAll('#ro_dateGrid .date-item').forEach(x=>x.classList.remove('selected'));
        el.classList.add('selected');
        selectedDate = new Date(d);
        // set hidden date immediately (YYYY-MM-DD)
        document.getElementById('ro_booking_date').value = selectedDate.toISOString().split('T')[0];
        // clear previously selected time when changing day
        selectedTime = null;
        document.getElementById('ro_booking_time').value = '';
        updateDisplay();
        // fetch time slots for this date
        const serviceId = document.getElementById('ro_service_id').value;
        fetchTimeSlots(serviceId, selectedDate.toISOString().split('T')[0]);
    }

    function selectTime(el, hhmm){
        document.querySelectorAll('#ro_timeGrid .time-item').forEach(x=>x.classList.remove('selected'));
        el.classList.add('selected');
        selectedTime = hhmm;
        document.getElementById('ro_booking_time').value = hhmm;
        updateDisplay();
    }

    function generateTimeSlots(slot, date){
        const slots = []; if (!slot) return slots;
        let sh, sm, eh, em;
        const startStr = String(slot.start_time || '');
        const endStr = String(slot.end_time || '');
        if (startStr.includes('T')) {
            // ISO with Z (UTC) → use UTC hours to avoid timezone drift
            const sDate = new Date(startStr);
            const eDate = new Date(endStr);
            sh = sDate.getUTCHours();
            sm = sDate.getUTCMinutes();
            eh = eDate.getUTCHours();
            em = eDate.getUTCMinutes();
        } else {
            [sh, sm] = startStr.split(':').map(Number);
            [eh, em] = endStr.split(':').map(Number);
        }
        const duration = parseInt(slot.duration, 10); if (isNaN(duration)) return slots;
        const start = new Date(date); start.setHours(sh||0, sm||0, 0, 0);
        const end = new Date(date); end.setHours(eh||0, em||0, 0, 0);
        const now = new Date();
        while(start < end){
            const cur = new Date(start);
            const hhmm = pad(cur.getHours())+':'+pad(cur.getMinutes());
            const isToday = cur.toDateString() === now.toDateString();
            const disabled = isToday && cur < now;
            slots.push({timeData: hhmm, disabled});
            start.setMinutes(start.getMinutes()+duration);
        }
        return slots;
    }

    function renderTimeSlots(slotDef, date, bookedSlots = []){
        const grid = document.getElementById('ro_timeGrid'); if (!grid) return;
        grid.innerHTML='';
        const normalizedBooked = (bookedSlots||[]).map(s=> s && s.includes(':') ? s.substring(0,5) : s);
        const timeSlots = generateTimeSlots(slotDef, date);
        const originalRaw = document.getElementById('ro_original_time').value || '';
        const originalHHMM = originalRaw ? originalRaw.substring(0,5) : '';
        const originalDate = (document.getElementById('ro_original_date').value || '').trim();
        timeSlots.forEach(s => {
            const div = document.createElement('div');
            div.className = 'time-item';
            div.textContent = s.timeData;
            div.setAttribute('data-time', s.timeData);
            let shouldDisable = s.disabled || normalizedBooked.includes(s.timeData);
            if (shouldDisable){
                div.classList.add('disabled');
            } else {
                div.addEventListener('click', function(){ selectTime(this, s.timeData); });
            }
            // Preselect only when rendering the original booking date; on other dates, no preselection
            if (originalHHMM && originalHHMM === s.timeData && originalDate && originalDate === String(date)){
                div.classList.add('selected');
                // Set form values but do not override disable behavior
                selectedTime = s.timeData;
                document.getElementById('ro_booking_time').value = s.timeData;
                updateDisplay();
            }
            grid.appendChild(div);
        });
    }

    function fetchDayMeta(serviceId, isoDate){
        // Get service details to seed slots and disabled dates if needed
        return $.get("{{ url('get-service-details') }}/"+serviceId+"?date="+isoDate);
    }

    function fetchTimeSlots(serviceId, isoDate){
        const url = "{{ url('get-service-time-slots') }}/"+serviceId+"?date="+isoDate;
        $.get(url, function(resp){
            if (resp && resp.status){
                // Prefer slots returned for this specific date; fall back to cachedSlots from get-service-details
                const slotDef = (resp.slots && resp.slots[0]) ? resp.slots[0] : cachedSlots;
                renderTimeSlots(slotDef, isoDate, resp.bookedSlots || []);
            } else {
                $('#ro_timeGrid').html('<div class="no-time-slots">No available time slots for this date</div>');
            }
        }).fail(function(){
            $('#ro_timeGrid').html('<div class="no-time-slots">Error loading time slots</div>');
        });
    }

    function initializeCalendarFor(serviceId, initialIsoDate){
        currentWeekStart = setToStartOfWeek(new Date(initialIsoDate || new Date()));
        selectedDate = initialIsoDate ? new Date(initialIsoDate) : null;
        // Seed selectedTime with original if present
        const originalRaw = document.getElementById('ro_original_time').value || '';
        selectedTime = originalRaw ? originalRaw.substring(0,5) : null;
        buildMonthOptions();
        renderDates([]);

        // seed slots for initial date
        const seedDate = (selectedDate ? selectedDate : new Date());
        const seedIso = seedDate.toISOString().split('T')[0];
        fetchDayMeta(serviceId, seedIso).done(function(resp){
            if (resp && resp.status){
                cachedSlots = (resp.slots && resp.slots[0]) ? resp.slots[0] : null;
                // Render quickly with any slots we have from details
                renderTimeSlots(cachedSlots, seedIso, resp.bookedSlots || []);
                document.getElementById('ro_booking_date').value = seedIso;
                // Then immediately fetch the authoritative slots/bookedSlots for this date
                fetchTimeSlots(serviceId, seedIso);
            } else {
                // Fallback: still try fetching time slots directly
                fetchTimeSlots(serviceId, seedIso);
            }
        }).fail(function(){
            // On error, still attempt time slots fetch so the grid isn't empty
            fetchTimeSlots(serviceId, seedIso);
        });

        // controls
        $('#ro_prevBtn').off('click').on('click', function(){ currentWeekStart.setDate(currentWeekStart.getDate()-7); renderDates([]); });
        $('#ro_nextBtn').off('click').on('click', function(){ currentWeekStart.setDate(currentWeekStart.getDate()+7); renderDates([]); });
        $('#ro_monthSelect').off('change').on('change', function(){
            const [y,m] = this.value.split('-').map(Number);
            const d = new Date(y, m, 1);
            currentWeekStart = setToStartOfWeek(d);
            renderDates([]);
        });

        updateDisplay();
    }

    // Save handler
    $('#ro_save_btn').on('click', function(){
        const newDate = $('#ro_booking_date').val();
        let newTime = $('#ro_booking_time').val();
        const originalDate = ($('#ro_original_date').val() || '').trim();
        let originalTime = ($('#ro_original_time').val() || '').trim();
        const serviceId = $('#ro_service_id').val();
        const bookingId = $('#ro_booking_id').val();

        if (!newDate || !newTime){
            Swal.fire({ text: 'Please select both date and time', icon: 'warning', confirmButtonText: 'Ok, got it!' });
            return;
        }
        // Normalize times to HH:MM for reliable comparison (original may be HH:MM:SS)
        if (originalTime && originalTime.length >= 5) originalTime = originalTime.substring(0,5);
        if (newTime && newTime.length >= 5) newTime = newTime.substring(0,5);
        if (originalDate === newDate && originalTime === newTime){
            Swal.fire({ text: 'Please change the date or time to reschedule the booking', icon: 'warning', confirmButtonText: 'Ok, got it!' });
            return;
        }

        // Submit reschedule
        $.ajax({
            url: '{{ route('booking.reschedule') }}',
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                booking_date: newDate,
                booking_time: newTime,
                service_id: serviceId,
                // reschedule payload
                booking_id: bookingId
            },
            success: function(resp){
                if (resp && resp.status === 'success'){
                    $('#reschedule-only').modal('hide');
                    Swal.fire({ text: 'Booking rescheduled successfully', icon: 'success', confirmButtonText: 'Ok, got it!' })
                        .then(()=>{ window.location.href = '/customer/my-booking'; });
                } else if (resp && resp.status === 'time_conflict') {
                    Swal.fire({
                        text: resp.message || 'Selected time is already booked. Please choose a different slot.',
                        icon: 'warning',
                        confirmButtonText: 'Ok, got it!'
                    });
                } else {
                    Swal.fire({ text: (resp && resp.message) ? resp.message : 'Error rescheduling booking', icon: 'error', confirmButtonText: 'Ok, got it!' });
                }
            },
            error: function(xhr){
                try {
                    const resp = xhr.responseJSON || {};
                    if (xhr.status === 422 && resp.status === 'time_conflict') {
                        Swal.fire({
                            text: resp.message || 'Selected time is already booked. Please choose a different slot.',
                            icon: 'warning',
                            confirmButtonText: 'Ok, got it!'
                        });
                        return;
                    }
                } catch(e) {}
                Swal.fire({ text: 'Error rescheduling booking', icon: 'error', confirmButtonText: 'Ok, got it!' });
            }
        });
    });

    // Public entrypoints
    window.openRescheduleOnly = function(serviceId, bookingId, originalDate, originalTime){
        $('#ro_service_id').val(serviceId||'');
        $('#ro_booking_id').val(bookingId||'');
        $('#ro_original_date').val(originalDate||'');
        $('#ro_original_time').val(originalTime||'');
        $('#ro_booking_date').val('');
        $('#ro_booking_time').val('');

        const initDate = originalDate || new Date().toISOString().split('T')[0];
        initializeCalendarFor(serviceId, initDate);
        $('#reschedule-only').modal('show');
    };

    // Button trigger on customer page
    $(document).on('click', '.reschedule-open-btn', function(){
        const $btn = $(this);
        const serviceId = $btn.data('id');
        const bookingId = $btn.data('booking-id');
        const originalDate = $btn.data('original-booking-date');
        const originalTime = $btn.data('original-booking-time');
        window.openRescheduleOnly(serviceId, bookingId, originalDate, originalTime);
    });
})();
</script>
@endpush


