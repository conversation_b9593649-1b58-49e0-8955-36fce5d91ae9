@extends('dashboard.layout.master')
<style>
    /* .error {
        color: red !important;
        font-weight: bold !important;
        margin-top: 5px;
        display: block;
    } */

        /* Ensure all validation messages are block-level and stay below inputs */
span.error {
    display: block !important;
    color: #f1416c; /* Metronic danger color */
    font-size: 13px;
    font-weight: 500;
    line-height: 1.4;
    width: 100%; /* ensures it doesn't wrap beside inputs */
}

/* For horizontal form fields that share a row */
.form-group,
.fv-row,
.mb-8 {
    position: relative;
}

/* Adjust alignment under Select2 and Flatpickr */
.select2-container + span.error,
.flatpickr-input + span.error {
    display: block !important;
    margin-top: 4px;
}


    .form-control.error,
    .form-select.error {
        border-color: red !important;
    }
</style>
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-coupon padding-block">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12 breadcrumbs">
                    <h6 class="sora black">{{ isset($discountcoupon) ? 'Edit' : 'Add' }} Discount Coupon</h6>
                    <p class="fs-14 sora light-black m-0">Discount & Coupon <span class="mx-3"><i
                                class="fa-solid fa-chevron-right right-arrow"></i></span>
                        {{ isset($discountcoupon) ? 'Edit' : 'Add' }} Discount Coupon </p>
                </div>
                <div class="col-md-12 add-coupon-star ">
                    <form id="discountCouponForm"
                        action="{{ isset($discountcoupon) ? route('discount-coupons.update', $discountcoupon->ids) : route('discount-coupons.store') }}"
                        method="POST" class="form-add-services">
                        @csrf
                        @if (isset($discountcoupon))
                            @method('PUT')
                        @endif
                        <input type="hidden" name="discount_type" value="{{ $type ?? 'service' }}">
                        <div class="row row-gap-5">
                            <div class="col-md-6">
                                <label for="coupon-name" class="form-label form-input-labels">Coupon Name</label>
                                <input type="text" class="form-control form-inputs-field" placeholder="Enter coupon name"
                                    id="name" name="name" maxlength="100"
                                    value="{{ isset($discountcoupon) ? $discountcoupon->name : old('name') }}">
                            </div>
                            <div class="col-md-6">
                                <label for="coupon-code" class="form-label form-input-labels">Coupon Code</label>
                                <input type="text" class="form-control form-inputs-field" placeholder="Enter coupon code"
                                    id="coupon-code" name="coupon_code" maxlength="50"
                                    value="{{ isset($discountcoupon) ? $discountcoupon->coupon_code : old('coupon_code') }}">
                            </div>
                            <div class="col-md-6">
                                <label for="type" class="form-label form-input-labels">Discount Type</label>

                                <select class="form-select form-select-field" id="type" name="type">
                                    <option></option> <!-- Empty option needed for placeholder -->
                                    <option value="percentage"
                                        {{ (isset($discountcoupon) ? $discountcoupon->type : old('type')) == 'percentage' ? 'selected' : '' }}>
                                        Percentage
                                    </option>
                                    <option value="amount"
                                        {{ (isset($discountcoupon) ? $discountcoupon->type : old('type')) == 'amount' ? 'selected' : '' }}>
                                        Amount
                                    </option>
                                </select>
                              
                                <!-- <select class="form-select form-select-field" id="type" name="type"
                                   >
                                    <option> </option>
                                    <option value="percentage"
                                        {{ (isset($discountcoupon) ? $discountcoupon->type : old('type', 'percentage')) == 'percentage' ? 'selected' : '' }}>
                                        Percentage</option>
                                    <option value="amount"
                                        {{ (isset($discountcoupon) ? $discountcoupon->type : old('type')) == 'amount' ? 'selected' : '' }}>
                                        Amount</option>
                                </select> -->

                            </div>
                            <div class="col-md-6">
                                <label for="discount" class="form-label form-input-labels">Discount Value</label>
                                <div class="input-group">
                                    <span class="input-group-text" id="discount-symbol">%</span>
                                    <input type="number" min="1" class="form-control form-inputs-field m-0"
                                        placeholder="Enter discount value" id="discount" name="discount"
                                        value="{{ isset($discountcoupon) ? $discountcoupon->discount : old('discount') }}" />
                                </div>
                                <label id="discount-error" class="error d-block" for="discount"></label>
                            </div>
                            <div class="col-md-6">
                                <label for="user-limit" class="form-label form-input-labels">User Limit</label>
                                <input type="number" min="1" class="form-control form-inputs-field m-0"
                                    placeholder="Enter user limit" id="user-limit" name="user_limit"
                                    value="{{ isset($discountcoupon) ? $discountcoupon->user_limit : old('user_limit') }}">
                            </div>
                            
                            @if($type === 'subscription')
                            <!-- Additional fields for subscription coupons -->
                            <div class="col-md-6">
                                <label for="duration" class="form-label form-input-labels">Duration (Months)</label>
                                <select class="form-select form-select-field" id="duration" name="duration">
                                    <option value="">Select duration</option>
                                    <option value="1" {{ old('duration') == '1' ? 'selected' : '' }}>1 Month</option>
                                    <option value="2" {{ old('duration') == '2' ? 'selected' : '' }}>2 Months</option>
                                    <option value="3" {{ old('duration') == '3' ? 'selected' : '' }}>3 Months</option>
                                    <option value="6" {{ old('duration') == '6' ? 'selected' : '' }}>6 Months</option>
                                    <option value="9" {{ old('duration') == '9' ? 'selected' : '' }}>9 Months</option>
                                    <option value="12" {{ old('duration') == '12' ? 'selected' : '' }}>12 Months</option>
                                </select>
                            </div>
                            @endif
                            @if($type === 'subscription')
                            <!-- Subscription Selection for Subscription Coupons -->
                            <div class="col-md-12">
                                <label for="subscription-select" class="form-label form-input-labels">Choose Subscriptions</label>
                                <select class="form-select form-select-field" id="subscription-select"
                                    name="subscriptions[]" data-control="select2" data-placeholder="Select Subscriptions"
                                    multiple>
                                    @foreach ($subscriptions as $subscription)
                                        <option value="{{ $subscription->id }}"
                                            {{ isset($discountcoupon) && $discountcoupon->subscriptions->contains('id', $subscription->id) ? 'selected' : (in_array($subscription->id, old('subscriptions', [])) ? 'selected' : '') }}>
                                            {{ $subscription->name }} - ${{ $subscription->price }}/{{ $subscription->interval }}
                                        </option>
                                    @endforeach
                                </select>
                                <label id="subscription-select-error" class="error" for="subscription-select"></label>
                            </div>
                            @else
                            <!-- Radio Buttons for Service Coupons -->
                            <div class="col-md-12">
                                <label for="category-service" class="form-label form-input-labels">
                                    @if (auth()->user()->hasRole('individual') || auth()->user()->hasRole('business'))
                                        Choose Services
                                    @else
                                        Choose Category or Services
                                    @endif
                                </label>
                                @if (auth()->user()->hasAnyRole(['admin', 'super admin']))
                                    <div class="d-flex gap-4 mb-3">
                                        <label for="filter-by-category" class="d-flex gap-2 align-items-center">
                                            <input class="form-check-input" type="radio" name="applies_to"
                                                value="category" id="filter-by-category"
                                                {{ (isset($discountcoupon) ? $discountcoupon->applies_to : old('applies_to', 'category')) == 'category' ? 'checked' : '' }}>
                                            <span>Category</span>
                                        </label>
                                        <label for="filter-by-service" class="d-flex gap-2 align-items-center">
                                            <input class="form-check-input" type="radio" name="applies_to" value="service"
                                                id="filter-by-service"
                                                {{ (isset($discountcoupon) ? $discountcoupon->applies_to : old('applies_to')) == 'service' ? 'checked' : '' }}>
                                            <span>Services</span>
                                        </label>
                                    </div>
                                @endif
                                <!-- Dropdown Fields -->
                                <div class="row row-gap-5">
                                    <!-- Category Dropdown -->
                                    @if (auth()->user()->hasAnyRole(['admin', 'super admin']))
                                        <div class="col-md-12 form-hide-box category-cuopon" id="category-dropdown-wrapper">
                                            <select class="form-select form-select-field" id="category-select"
                                                name="categories[]" data-control="select2"
                                                data-placeholder="Select Categories" multiple>
                                                @foreach ($categories as $category)
                                                    <option value="{{ $category->id }}"
                                                        {{ isset($discountcoupon) && $discountcoupon->categories->contains('id', $category->id) ? 'selected' : (in_array($category->id, old('categories', [])) ? 'selected' : '') }}>
                                                        {{ $category->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            <label id="category-select-error" class="error" for="category-select" style="pointer-events: none;">
                                            </label>
                                        </div>
                                    @endif

                                    <!-- Service Dropdown -->
                                    <div class="col-md-12 form-hide-box category-cuopon" id="service-dropdown-wrapper">
                                        <select class="form-select form-select-field" id="service-select"
                                            name="services[]" data-control="select2" data-placeholder="Select Services"
                                            multiple>
                                            @foreach ($services as $service)
                                                <option value="{{ $service->id }}"
                                                    {{ isset($discountcoupon) && $discountcoupon->services->contains('id', $service->id) ? 'selected' : (in_array($service->id, old('services', [])) ? 'selected' : '') }}>
                                                    {{ $service->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                         <label id="service-select-error" class="error" for="service-select" style="pointer-events: none;"> </label>
                                    </div>
                                   
                                </div>
                                
                            </div>
                            @endif
                            @if($type === 'service')
                            <div class="col-md-6">
                                <label class="form-label form-input-labels">Start Date</label>
                                <div class="flatpickr input-group form-control form-inputs-field" data-wrap="true">
                                    <input type="text" id="start_date" class="" placeholder="Select start date"
                                        data-input name="start_date"
                                        value="{{ isset($discountcoupon) ? $discountcoupon->start_date : old('start_date') }}">
                                    <button class="input-button calender-button" type="button" title="toggle"
                                        data-toggle>
                                        <i class="fa-regular fa-calendar"></i>
                                    </button>
                                </div>
                                <label id="start_date-error" class="error" > </label>
                            </div>
                            @endif
                            <div class="col-md-6">
                                <label  class="form-label form-input-labels">
                                    {{ $type === 'subscription' ? 'Expiry Date' : 'End Date' }}
                                </label>
                                <div class="flatpickr input-group form-control form-inputs-field" data-wrap="true">
                                    <input type="text" id="end_date" class="" placeholder="Select {{ $type === 'subscription' ? 'expiry' : 'end' }} date"
                                        data-input name="end_date"
                                        value="{{ isset($discountcoupon) ? $discountcoupon->end_date : old('end_date') }}">
                                    <button class="input-button calender-button" type="button" title="toggle"
                                        data-toggle>
                                        <i class="fa-regular fa-calendar"></i>
                                    </button>
                                </div>
                                <label id="end_date-error" class="error" > </label>
                            </div>
                            
                            <div class="col-md-12 mt-6">
                                <button type="submit" class="add-btn">
                                    {{ isset($discountcoupon) ? 'Update' : 'Add' }}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <!-- jQuery Script -->
    <script>
        // On radio change
        $(document).on('change', 'input[name="applies_to"]', function() {
            console.log('applies_to changed to:', this.value);
            $('#category-select, #service-select').valid();
            toggleFilterFields(this.value);
        });

        function toggleFilterFields(value) {
            if (value === 'category') {
                console.log('category');
                $('#category-dropdown-wrapper').show();
                $('#service-dropdown-wrapper').hide();
            } else {
                console.log('service');
                $('#category-dropdown-wrapper').hide();
                $('#service-dropdown-wrapper').show();
            }
        }

        
        $(document).ready(function() {

            $('#type').select2({
                placeholder: "Select discount type",
                allowClear: true,
                width: '100%'
            });


            // Function to update discount symbol and validation based on type
            function updateDiscountField(type) {
                const symbol = $('#discount-symbol');
                const discountField = $('#discount');

                if (type === 'amount') {
                    symbol.text('$');
                    discountField.attr('placeholder', 'Enter discount amount');
                    // Remove max validation for amount type
                    if (discountField.length && discountField.rules) {
                        discountField.rules('remove', 'max');
                    }
                } else {
                    symbol.text('%');
                    discountField.attr('placeholder', 'Enter discount percentage');
                    // Add max validation for percentage type
                    if (discountField.length && discountField.rules) {
                        discountField.rules('add', {
                            max: 100,
                            messages: {
                                max: "Discount percentage cannot exceed 100%"
                            }
                        });
                    }
                }
            }

            // Wait for Select2 to initialize, then set up events
            setTimeout(function() {
                // Initial load - check current selection
                toggleFilterFields($('input[name="applies_to"]:checked').val());

                // Initial load - update discount field based on selected type
                const initialType = $('#type').val() || 'percentage';
                updateDiscountField(initialType);

                // On discount type change - handle both regular select and select2
                $('#type').on('change', function() {
                    updateDiscountField(this.value);
                });

                // Also handle select2 change event
                $('#type').on('select2:select', function(e) {
                    updateDiscountField(e.params.data.id);
                });

                // Fallback: Direct event delegation for any select changes
                $(document).on('change', 'select[name="type"]', function() {
                    updateDiscountField(this.value);
                });
                
                // Additional approach: Check select value on any click in the select area
                $('.select2-container').on('click', function() {
                    setTimeout(function() {
                        const currentValue = $('#type').val();
                        if (currentValue) {
                            updateDiscountField(currentValue);
                        }
                    }, 100);
                });
                
            }, 1000); // Wait 1 second for Select2 to initialize
            
            // Fallback: Check for changes every 500ms as a last resort
            let lastTypeValue = $('#type').val();
            setInterval(function() {
                const currentTypeValue = $('#type').val();
                if (currentTypeValue !== lastTypeValue) {
                    updateDiscountField(currentTypeValue);
                    lastTypeValue = currentTypeValue;
                }
            }, 500);

            // Custom validation methods
            $.validator.addMethod("dateGreaterThan", function(value, element, params) {
                if (!value || !$(params).val()) return true;
                var startDate = new Date($(params).val());
                var endDate = new Date(value);
                return endDate >= startDate;
            }, "End date must be after or equal to start date");

            $.validator.addMethod("categoryRequired", function(value, element) {
                if ($('input[name="applies_to"]:checked').val() === 'category') {
                    return $('#category-select').val() && $('#category-select').val().length > 0;
                }
                return true;
            }, "Please select at least one category");

            $.validator.addMethod("serviceRequired", function(value, element) {
                if ($('input[name="applies_to"]:checked').val() === 'service') {
                    return $('#service-select').val() && $('#service-select').val().length > 0;
                }
                return true;
            }, "Please select at least one service");

            $.validator.addMethod("subscriptionRequired", function(value, element) {
                // Only validate if this is a subscription coupon
                if ($('input[name="discount_type"]').val() === 'subscription') {
                    return $('#subscription-select').val() && $('#subscription-select').val().length > 0;
                }
                return true;
            }, "Please select at least one subscription");

            $.validator.addMethod("durationRequired", function(value, element) {
                // Only validate if this is a subscription coupon
                if ($('input[name="discount_type"]').val() === 'subscription') {
                    return value && value !== '';
                }
                return true;
            }, "Please select a duration for subscription coupons");

            // Debounce function to delay validation
            function debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }

            // Check if coupon code already exists (async version)
            $.validator.addMethod("couponCodeUnique", function(value, element) {
                var validator = this;
                var currentId = "{{ isset($discountcoupon) ? $discountcoupon->ids : '' }}";
                
                // Skip validation for empty values (handled by required rule)
                if (!value || value.trim() === '') {
                    return true;
                }
                
                // Skip validation if less than minimum length (handled by minlength rule)
                if (value.length < 3) {
                    return true;
                }

                // Clear any existing validation state
                $(element).removeClass('error');
                $(element).next('label.error').remove();
                
                // Show loading state
                var $loadingIndicator = $('<span class="coupon-loading" style="color: #007bff; font-size: 12px; margin-left: 5px;">Checking...</span>');
                $(element).after($loadingIndicator);

                // Make async AJAX request
                $.ajax({
                    url: "{{ route('discountcoupons.check-coupon-code') }}",
                    type: "POST",
                    async: true,
                    data: {
                        coupon_code: value,
                        id: currentId,
                        discount_type: $('input[name="discount_type"]').val(),
                        _token: "{{ csrf_token() }}"
                    },
                    success: function(response) {
                        $loadingIndicator.remove();
                        
                        if (!response.available) {
                            // Show error
                            $(element).addClass('error');
                            var errorLabel = $('<label class="error" for="coupon-code">This coupon code already exists</label>');
                            $(element).after(errorLabel);
                        } else {
                            // Show success indicator
                            var $successIndicator = $('<span class="coupon-success" style="color: #28a745; font-size: 12px; margin-left: 5px;">✓ Available</span>');
                            $(element).after($successIndicator);
                            setTimeout(() => $successIndicator.fadeOut(), 2000);
                        }
                    },
                    error: function() {
                        $loadingIndicator.remove();
                        // On error, assume validation passes to not block form submission
                        console.log('Error checking coupon code availability');
                    }
                });
                
                // Return true for async validation (we handle errors manually)
                return true;
            }, "This coupon code already exists");

            // Form validation
            $(".form-add-services").validate({
                errorClass: "error",
                errorElement: "span",
               errorPlacement: function (error, element) {
                    if (element.attr("name") == "categories[]") {
                        error.insertAfter($('#category-select').next('.select2')); 
                    } else if (element.attr("name") == "services[]") {
                        error.insertAfter($('#service-select').next('.select2')); 
                    } else if (element.hasClass("select2-hidden-accessible")) {
                        error.insertAfter(element.next('.select2')); // ✅ fixes "Discount Type"
                    } else if (element.closest('.flatpickr').length) {
                        error.insertAfter(element.closest('.flatpickr'));
                    } else {
                        error.insertAfter(element);
                    }
                },


                rules: {
                    name: {
                        required: true,
                        minlength: 2
                    },
                    coupon_code: {
                        required: true,
                        minlength: 3
                        // couponCodeUnique validation is handled separately with debouncing
                    },
                    type: {
                        required: true
                    },
                    discount: {
                        required: true,
                        number: true,
                        min: 1
                        // max will be added dynamically for percentage type
                    },
                    user_limit: {
                        required: true,
                        number: true,
                        min: 1
                    },
                    start_date: {
                        required: function() {
                            return $('input[name="discount_type"]').val() === 'service';
                        }
                    },
                    end_date: {
                        required: true,
                        dateGreaterThan: function() {
                            return $('input[name="discount_type"]').val() === 'service' ? "#start_date" : null;
                        }
                    },
                    duration: {
                        durationRequired: true
                    },
                    "categories[]": {
                        categoryRequired: true
                    },
                    "services[]": {
                        serviceRequired: true
                    },
                    "subscriptions[]": {
                        subscriptionRequired: true
                    }
                },
                messages: {
                    name: {
                        required: "Coupon name is required",
                        minlength: "Coupon name must be at least 2 characters"
                    },
                    coupon_code: {
                        required: "Coupon code is required",
                        minlength: "Coupon code must be at least 3 characters"
                    },
                    type: {
                        required: "Discount type is required"
                    },
                    discount: {
                        required: "Discount value is required",
                        number: "Please enter a valid number",
                        min: "Discount must be at least 1"
                    },
                    user_limit: {
                        required: "User limit is required",
                        number: "Please enter a valid number",
                        min: "User limit must be at least 1"
                    },
                    start_date: {
                        required: "Start date is required for service coupons"
                    },
                    end_date: {
                        required: "End date is required"
                    },
                    duration: {
                        durationRequired: "Duration is required for subscription coupons"
                    },
                    "subscriptions[]": {
                        subscriptionRequired: "Please select at least one subscription"
                    }
                },
                submitHandler: function(form) {
                    form.submit();
                }
            });

            // // Trigger validation when radio buttons change
            // $('input[name="applies_to"]').on('change', function() {

            // });

            // Trigger validation when select values change
            $('#category-select, #service-select').on('change', function() {
                $(this).valid();
            });

            // Clear validation errors for all form fields
            
            // 1. Select2 dropdowns - handle both regular change and select2 events
            $('#type, #duration, #subscription-select').on('change select2:select select2:unselect', function() {
                $(this).removeClass('error');
                $(this).next('label.error').remove();
                $(this).valid();
            });

            // 2. Regular text inputs - clear errors on input
            $('#name, #discount, #user-limit').on('input keyup paste', function() {
                $(this).removeClass('error');
                $(this).next('label.error').remove();
                $(this).valid();
            });

            // 3. Flatpickr date fields - need special handling
            // Wait for Flatpickr to initialize, then attach events
            setTimeout(function() {
                // Get Flatpickr instances if they exist
                var startDateFlatpickr = document.querySelector('#start_date');
                var endDateFlatpickr = document.querySelector('#end_date');

                if (startDateFlatpickr && startDateFlatpickr._flatpickr) {
                    startDateFlatpickr._flatpickr.config.onChange.push(function(selectedDates, dateStr, instance) {
                        $('#start_date').removeClass('error');
                        $('#start_date').closest('.flatpickr').next('label.error').remove();
                        $('#start_date').valid();
                    });
                }

                if (endDateFlatpickr && endDateFlatpickr._flatpickr) {
                    endDateFlatpickr._flatpickr.config.onChange.push(function(selectedDates, dateStr, instance) {
                        $('#end_date').removeClass('error');
                        $('#end_date').closest('.flatpickr').next('label.error').remove();
                        $('#end_date').valid();
                    });
                }
            }, 1500); // Wait for Flatpickr to fully initialize

            // 4. Fallback: Direct input change for date fields (in case Flatpickr events don't work)
            $('#start_date, #end_date').on('change input', function() {
                $(this).removeClass('error');
                $(this).closest('.flatpickr').next('label.error').remove();
                $(this).valid();
            });

            // 5. Radio buttons for applies_to
            $('input[name="applies_to"]').on('change', function() {
                // Clear errors from both category and service selects when switching
                $('#category-select, #service-select').removeClass('error');
                $('#category-select-error, #service-select-error').remove();
            });

            // Debounced coupon code validation
            var debouncedCouponValidation = debounce(function() {
                var couponCode = $('#coupon-code').val();
                if (couponCode && couponCode.length >= 3) {
                    // Clear any existing indicators
                    $('.coupon-loading, .coupon-success').remove();
                    
                    // Call the validation method directly
                    var element = $('#coupon-code')[0];
                    var currentId = "{{ isset($discountcoupon) ? $discountcoupon->ids : '' }}";
                    
                    // Clear any existing validation state
                    $(element).removeClass('error');
                    $(element).next('label.error').remove();
                    
                    // Show loading state
                    var $loadingIndicator = $('<span class="coupon-loading" style="color: #007bff; font-size: 12px; margin-left: 5px;">Checking...</span>');
                    $(element).after($loadingIndicator);

                    // Make async AJAX request
                    $.ajax({
                        url: "{{ route('discountcoupons.check-coupon-code') }}",
                        type: "POST",
                        async: true,
                        data: {
                            coupon_code: couponCode,
                            id: currentId,
                            discount_type: $('input[name="discount_type"]').val(),
                            _token: "{{ csrf_token() }}"
                        },
                        success: function(response) {
                            $loadingIndicator.remove();
                            
                            if (!response.available) {
                                // Show error
                                $(element).addClass('error');
                                var errorLabel = $('<label class="error" for="coupon-code">This coupon code already exists</label>');
                                $(element).after(errorLabel);
                            } else {
                                // Show success indicator
                                var $successIndicator = $('<span class="coupon-success" style="color: #28a745; font-size: 12px; margin-left: 5px;">✓ Available</span>');
                                $(element).after($successIndicator);
                                setTimeout(() => $successIndicator.fadeOut(), 2000);
                            }
                        },
                        error: function() {
                            $loadingIndicator.remove();
                            // On error, assume validation passes to not block form submission
                            console.log('Error checking coupon code availability');
                        }
                    });
                }
            }, 500); // Wait 500ms after user stops typing

            // Attach debounced validation to coupon code input
            $('#coupon-code').on('input keyup paste', function() {
                // Clear any existing error states for immediate feedback
                $('.coupon-loading, .coupon-success').remove();
                $(this).removeClass('error');
                $(this).next('label.error').remove();
                
                // Trigger debounced validation
                debouncedCouponValidation();
            });

            // Also validate on form submission
            $('.form-add-services').on('submit', function(e) {
                var couponCode = $('#coupon-code').val();
                if (couponCode && couponCode.length >= 3) {
                    // Check if we have a pending validation
                    if ($('.coupon-loading').length > 0) {
                        e.preventDefault();
                        alert('Please wait for coupon code validation to complete.');
                        return false;
                    }
                    
                    // Check if there's an error
                    if ($('#coupon-code').hasClass('error')) {
                        e.preventDefault();
                        return false;
                    }
                }
            });
        });
    </script>
@endpush
