<div class="row ">
    <div class="col-md-8">
        <div class="position-absolute previous-box d-flex justify-content-between">
            <a href="{{ '/' }}"> <span class="back-arrow" data-prev="#step1"><i
                        class="fa-solid fa-arrow-left"></i></span> </a>
            <!-- <span><i class="back-arrow fa-solid fa-xmark"></i></span> -->
        </div>
        <!-- Card for Booking Details -->
        <div class="row row-gap-5">
            <div class="col-md-4">
                <p class="step-title fs-24 sora semi_bold ms-8">Your Cart</p>
            </div>
            @forelse($cards as $card)
                <div class="col-md-12 cart-item" data-service-id="{{ $card['service']['ids'] }}" data-booking-date="{{ $card['booking_date'] ?? '' }}" data-booking-time="{{ $card['booking_time'] ?? '' }}" data-cart-item-id="{{ $card['cart_item_id'] ?? '' }}">
                    <div class="card card-box step-card">
                        <div class="card-header align-items-center p-0 pb-3">
                            <div>
                                <p class="card-title fs-16 black regular">{{ $card['service']['name'] }}</p>
                                <p class="card-text fs-14 regular light-black opacity-6 d-inline">
                                    {{ $card['service']['duration'] }} mins</p><span class="deep-blue fs-14 regular">
                                    {{ $card['service']['user']['name'] }}</span>
                            </div>
                            <!-- Buttons for Edit and Delete -->
                            <div class="d-flex justify-content-end align-items-center gap-5">
                                <p class="fs-16 semi_bold m-0 black item-price">
                                    ${{ number_format($card['service']['price'] ?? 0, 2) }}</p>
                                <div class="d-flex">
                                    <button
                                        class="add-to-cart-btn drop-btn d-flex gap-3 align-items-center btn btn-outline-secondary  me-2 py-2 px-3 text-center fs-16  deep-blue"
                                        data-bs-toggle="modal" data-id="{{ $card['service']['ids'] }}"
                                        data-booking-date="{{ $card['booking_date'] ?? '' }}"
                                        data-booking-time="{{ $card['booking_time'] ?? '' }}"
                                        data-location-type="{{ $card['loc'] ?? '' }}"
                                        data-comments="{{ $card['comments'] ?? '' }}"
                                        data-user_id="{{ $card['user_id'] ?? '' }}"
                                        data-address="{{ $card['address'] ?? '' }}"
                                        data-category="{{ $card['category'] ?? '' }}"
                                        data-save-booking-address="{{ $card['save_booking_address'] ?? '' }}"
                                        data-lat="{{ $card['lat'] ?? '' }}" data-lng="{{ $card['lng'] ?? '' }}"
                                        data-selected-professionals="{{ isset($card['selected_professionals']) ? json_encode($card['selected_professionals']) : '[]' }}"
                                        data-is-edit="true" data-operation-type="cart-edit" data-cart-item-id="{{ $card['cart_item_id'] ?? '' }}" data-bs-target="#service-details"><i
                                            class="bi bi-pencil p-0"></i>
                                        <span>Edit Details</span></button>
                                    <button
                                        class="drop-btn delete-cart-item btn btn-outline-danger py-2 px-3 text-center"
                                        data-service-id="{{ $card['service']['ids'] }}"
                                        data-booking-date="{{ $card['booking_date'] ?? '' }}"
                                        data-booking-time="{{ $card['booking_time'] ?? '' }}"
                                        data-cart-item-id="{{ $card['cart_item_id'] ?? '' }}">
                                        <i class="bi bi-trash p-0 red"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body d-flex p-0 gap-20 flex-wrap pt-5">
                            <div>
                                <!-- Date & Time -->
                                <p class="card-text fs-16 light-black opacity-6 fs-14 normal">
                                    <i class="bi bi-calendar-week me-2"></i>
                                    {{ $card['booking_date'] ? \Carbon\Carbon::parse($card['booking_date'])->format('d M Y') : '' }}
                                </p>
                                @php
                                    $startTime = \Carbon\Carbon::createFromFormat('H:i', $card['booking_time']);
                                    $endTime = $startTime->copy()->addMinutes($card['service']['duration']);
                                @endphp
                                <p class="card-text fs-16 light-black opacity-6 fs-14 normal">
                                    <i class="bi bi-clock me-2"></i>
                                    {{ $startTime->format('H:i') }} - {{ $endTime->format('H:i') }}
                                    ({{ $card['service']['duration'] }} mins duration)
                                </p>

                            </div>
                            <div>
                                <!-- Location and Description -->
                                <p class="card-text fs-16 light-black opacity-6 fs-14 normal">
                                    <i class="bi bi-geo-alt me-2"></i> {{ $card['address'] ?? '' }}
                                </p>
                                <p class="card-text fs-16 light-black opacity-6 fs-14 normal">
                                    <i class="bi bi-chat-left-dots me-2"></i> {{ $card['comments'] ?? '' }}
                                </p>
                                <!-- Selected Professionals -->
                                @if (isset($card['selected_professionals']) && !empty($card['selected_professionals']))
                                    <p class="card-text fs-16 light-black opacity-6 fs-14 normal">
                                        <i class="bi bi-people me-2"></i>
                                        <strong>Professionals:</strong>
                                        @foreach ($card['selected_professionals'] as $index => $professional)
                                            {{ $professional['name'] ?? 'Professional #' . $professional['id'] }}
                                            @if (!$loop->last)
                                                ,
                                            @endif
                                        @endforeach
                                    </p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-md-12">
                    <div class="empty-cart-container text-center py-5">
                        <div class="empty-cart-icon mb-4">
                            <i class="bi bi-cart-x" style="font-size: 4rem; color: #6c757d;"></i>
                        </div>
                        <h3 class="empty-cart-title fs-24 sora semi_bold mb-3" style="color: #495057;">Your Cart is
                            Empty</h3>
                        <p class="empty-cart-message fs-16 sora regular mb-4"
                            style="color: #6c757d; max-width: 400px; margin: 0 auto;">
                            Looks like you haven't added any services to your cart yet. Browse our services and find the
                            perfect treatment for you.
                        </p>
                        <a href="{{ route('website_services') }}"
                            class="btn btn-primary px-4 py-2 fs-16 sora semi_bold"
                            style="background-color: #020C87; border-color: #020C87;">
                            <i class="bi bi-search me-2"></i>Browse Services
                        </a>
                    </div>
                </div>
            @endforelse
        </div>
    </div>
    <div class="col-md-4">
        @if (session()->has('booking_cards') && count(session('booking_cards')) > 0)
            <div class="card card-box p-0 cart-box step-card">
                <div class="card-header py-5">
                    <div class="d-flex gap-2 align-items-center">
                        <img src="{{ asset('website').'/'.$user->profile->pic }}"
                            class="rounded-pill w-50px h-50px cart-image" alt="cart-image"
                            onerror="this.src='{{ asset('website/assets/images/image_input_holder.png') }}'">

                        <div>
                            <p class="sora semi_bold deep-blue m-0">{{$user->profile->company_name}}</p>
                            <p class="fs-14 sora semi_bold m-0 black"><i
                                    class="fa-solid fa-star fs-14 review-icon mx-1"></i>{{ $user->averageRating > 0 ? $user->averageRating : 'No rating' }}
                                <span class="normal deep-blue ms-1">({{ $reviews->count() }})</span>
                                <span class="light-black ms-1 me-2"> @include('svg.dot')
                                    {{$user->profile->location}}</span>
                            </p>
                        </div>

                    </div>

                </div>
                <div class="card-body d-flex flex-column gap-4">
                    @foreach ($cards as $card)
                        <div class="d-flex justify-content-between flex-row border-bottom pb-3 cart-item"
                            data-service-id="{{ $card['service']['ids'] }}" data-booking-date="{{ $card['booking_date'] ?? '' }}" data-booking-time="{{ $card['booking_time'] ?? '' }}" data-cart-item-id="{{ $card['cart_item_id'] ?? '' }}">
                            <div>
                                <p class="card-title fs-16 black regular">{{ $card['service']['name'] ?? 'Service' }}
                                </p>
                                <p class="card-text fs-14 regular light-black opacity-6 d-inline">
                                    {{ $card['service']['duration'] ?? '0' }} mins with
                                    <span
                                        class="deep-blue fs-14 regular">{{ $card['service']['user']['name'] ?? 'Provider' }}</span>
                                </p>
                            </div>
                            <p class="fs-16 semi_bold m-0 black item-price">
                                ${{ number_format($card['service']['price'] ?? 0, 2) }}</p>
                        </div>
                    @endforeach
                    {{-- <div class="d-flex justify-content-between">
                        <p class="card-title fs-16 black regular">VAT</p>
                        <p class="fs-16 semi_bold m-0 black">$17.84</p>
                    </div> --}}
                    <div class="position-relative">
                        <label for="coupon" class="form-label form-input-labels">Apply Coupon Code</label>
                        <input type="text" class="form-control form-input" name="coupon" id="coupon"
                            placeholder="Enter Coupon code">
                        <button type="button" class="button blue-button px-5 py-2 position-absolute top-10"
                            id="applyCouponBtn">Apply</button>
                        <button type="button" class="btn btn-outline-danger mt-1 px-3 py-2 position-absolute top-10"
                            id="removeCouponBtn" style="display: none;">
                            <i class="fa-solid fa-xmark text-white ms-1"></i>
                        </button>
                        <div id="couponError" class="text-danger mt-3" style="display: none;"></div>
                        <div id="couponSuccess" class="text-success mt-3" style="display: none;"></div>
                    </div>

                    <!-- Applied Coupon Display -->
                    {{-- <div id="appliedCouponDisplay"
                        class="d-flex justify-content-between align-items-center p-3 bg-light rounded mt-3"
                        style="display: none !important;">
                        <div class="d-flex gap-2 align-items-center">
                            <img src="{{ asset('website') }}/assets/images/coupon-code.png"
                                class="rounded-pill w-35px h-35px object-fit-contain" alt="coupon logo">
                            <div>
                                <p class="m-0 sora fs-14 semi_bold" id="couponSavedAmount">YAY! You saved $0</p>
                                <p class="fs-14 normal light-black opacity-6 m-0">Coupon Applied</p>
                            </div>
                        </div>
                        <button type="button" class="btn btn-link p-0" id="removeCouponDisplay">
                            <i class="fa-solid fa-xmark text-danger"></i>
                        </button>
                    </div> --}}
                </div>
                @php
                    $total = $cards->sum('service.price');
                @endphp
                <div class="card-footer">
                    <div class="d-flex justify-content-between mb-5">
                        <p class="card-title fs-16 black  semi_bold">Total</p>
                        <p class="fs-16 semi_bold m-0 deep-blue cart-total">
                            ${{ number_format($total, 2) }}
                        </p>
                    </div>
                    <a href="{{ url('customer/pay') . '?total=' . $total . '&coupon_code=' . ($couponCode ?? '') . '&discount_amount=' . ($discountAmount ?? 0) . '&original_total=' . ($originalTotal ?? $total) }}"
                        class="button w-100 blue-button" id="goToPaymentStep"
                        data-original-total="{{ $total }}" data-base-url="{{ url('customer/pay') }}">
                        Continue
                    </a>

                </div>
            </div>
        @endif
    </div>
</div>

@include('dashboard.templates.modal.add-service-details-modal')
