<?php

namespace App\Http\Controllers;

use App\Models\{Booking, Coupon, CouponUsage, DiscountCoupon, Notification, Service, User, Category, Staff, UserSubscription};
use Illuminate\Http\Request;
use Illuminate\Support\Facades\{View, Session, DB, Log};

class ThemeController extends Controller
{
    function __construct() {}
    public function permissions()
    {
        return view('theme.user-management.permissions');
    }

    public function professional_account()
    {
        return view('auth.professional_account_stepper');
    }

    public function dashboard()
    {
        return view('dashboard.index');
    }
    public function testing()
    {
        return view('dashboard.customer.testing');
    }
    public function familyFriends()
    {
        return view('dashboard.customer.family-friends');
    }
    public function addFriends()
    {
        return view('dashboard.customer.add-friend');
    }
    public function customerWallet()
    {
        $transactions = Booking::where('user_id', auth()->user()->id)->with(['provider', 'service'])->where('status', '!=', 2)->latest()->paginate(10);
        $refunds = Booking::where('user_id', auth()->user()->id)->where('status', 2)->with(['provider', 'service'])->latest()->paginate(10);
        return view('dashboard.customer.customer-wallet', compact('transactions', 'refunds'));
    }

    public function filterCustomerWallet(Request $request, $type)
    {
        // Validate type parameter
        if (!in_array($type, ['transactions', 'refunds'])) {
            return response()->json(['success' => false, 'message' => 'Invalid type'], 400);
        }

        $query = Booking::where('user_id', auth()->user()->id)->with(['provider', 'service']);

        // Apply status filter based on type
        if ($type === 'transactions') {
            $query->where('status', '!=', 2);
        } else {
            $query->where('status', 2);
        }

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->whereHas('provider', function ($providerQuery) use ($search) {
                    $providerQuery->where('name', 'like', "%{$search}%");
                })
                    ->orWhereHas('service', function ($serviceQuery) use ($search) {
                        $serviceQuery->where('name', 'like', "%{$search}%");
                    });
            });
        }

        // Apply date filter
        if ($request->filled('date_from') && $request->filled('date_to')) {
            $query->whereBetween('booking_date', [$request->date_from, $request->date_to]);
        }

        $data = $query->latest()->paginate(10);

        // Append search parameters to pagination links
        $data->appends($request->only(['search', 'date_from', 'date_to']));

        // Use appropriate partial view based on type
        $viewName = $type === 'transactions' ? 'transaction-history-table' : 'refund-history-table';

        // Assign data to appropriate variable name for the view
        if ($type === 'transactions') {
            $transactions = $data;
            $viewData = compact('transactions');
        } else {
            $refunds = $data;
            $viewData = compact('refunds');
        }

        $html = view("dashboard.customer.partials.{$viewName}", $viewData)->render();
        $paginationHtml = $data->hasPages() ? $data->links('pagination::bootstrap-4')->render() : '';

        return response()->json([
            'success' => true,
            'html' => $html,
            'pagination' => $paginationHtml,
            'count' => $data->count(),
            'total' => $data->total(),
            'hasPages' => $data->hasPages()
        ]);
    }

    public function exportCustomerWallet(Request $request, $type)
    {
        // Validate type parameter
        if (!in_array($type, ['transactions', 'refunds'])) {
            return response()->json(['success' => false, 'message' => 'Invalid type'], 400);
        }

        $query = Booking::where('user_id', auth()->user()->id)->with(['provider', 'service']);

        // Apply status filter based on type
        if ($type === 'transactions') {
            $query->where('status', 1);
        } else {
            $query->where('status', 2);
        }

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->whereHas('provider', function ($providerQuery) use ($search) {
                    $providerQuery->where('name', 'like', "%{$search}%");
                })
                    ->orWhereHas('service', function ($serviceQuery) use ($search) {
                        $serviceQuery->where('name', 'like', "%{$search}%");
                    });
            });
        }

        // Apply date filter
        if ($request->filled('date_from') && $request->filled('date_to')) {
            $query->whereBetween('booking_date', [$request->date_from, $request->date_to]);
        }

        $data = $query->latest()->get();

        // Use appropriate export class and filename based on type
        if ($type === 'transactions') {
            $exportClass = new \App\Exports\CustomerTransactionsExport($data);
            $filename = 'transaction-history-' . now()->format('Y-m-d') . '.xlsx';
        } else {
            $exportClass = new \App\Exports\CustomerRefundsExport($data);
            $filename = 'refund-history-' . now()->format('Y-m-d h:i') . '.xlsx';
        }

        return \Excel::download($exportClass, $filename);
    }

    public function cart()
    {
        $cards = collect(session()->get('booking_cards'));
        if ($cards->isNotEmpty()) {
            $user = User::find($cards->first()['service']['user_id']);
            $reviews = $user->receivedReviews;
        } else {
            $user = null;
            $reviews = null;
        }
        return view('dashboard.customer.cart', compact('cards', 'reviews', 'user'));
    }

    public function removeCartItem(Request $request)
    {
        try {
            $serviceId = $request->input('service_id');
            $bookingDate = $request->input('booking_date');
            $bookingTime = $request->input('booking_time');

            // Get current cart items from session
            $cartItems = session()->get('booking_cards', []);

            // Find and remove the matching item
            $updatedCart = array_filter($cartItems, function ($item) use ($serviceId, $bookingDate, $bookingTime) {
                return !(
                    $item['service']['ids'] == $serviceId &&
                    $item['booking_date'] == $bookingDate &&
                    $item['booking_time'] == $bookingTime
                );
            });

            // Re-index the array to maintain proper indexing
            $updatedCart = array_values($updatedCart);

            // Update session with the filtered cart
            session()->put('booking_cards', $updatedCart);

            return response()->json([
                'status' => true,
                'message' => 'Item removed from cart successfully',
                'cart_count' => count($updatedCart)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to remove item from cart: ' . $e->getMessage()
            ], 500);
        }
    }

    public function validateCoupon(Request $request)
    {
        try {
            $couponCode = $request->input('coupon_code');

            if (empty($couponCode)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Please enter a coupon code'
                ]);
            }

            // Get current cart items from session
            $cartItems = session()->get('booking_cards', []);
            if (empty($cartItems)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Your cart is empty'
                ]);
            }

            // Calculate current cart total
            $cartTotal = collect($cartItems)->sum(function ($item) {
                return $item['service']['price'] ?? 0;
            });

            // Get coupon from database (service coupons only)
            $coupon = DiscountCoupon::where('coupon_code', $couponCode)
                ->where('status', 1)
                ->where('discount_type', 'service')
                ->where('start_date', '<=', now())
                ->where('end_date', '>=', now())
                ->where('user_limit', '>', 0)
                ->first();

            if (!$coupon) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid or expired coupon code'
                ]);
            }

            // Check if coupon has usage limit
            if ($coupon->usage_limit && $coupon->used_count >= $coupon->usage_limit) {
                return response()->json([
                    'status' => false,
                    'message' => 'This coupon has reached its usage limit'
                ]);
            }

            // Check minimum amount requirement
            if ($cartTotal < $coupon->min_amount) {
                return response()->json([
                    'status' => false,
                    'message' => "Minimum order amount of $" . $coupon->min_amount . " required for this coupon"
                ]);
            }

            // Calculate discount based on type
            $discount = 0;
            if ($coupon->type === 'percentage') {
                $discount = ($cartTotal * $coupon->discount) / 100;
            } elseif ($coupon->type === 'amount') {
                $discount = $coupon->discount;
            } else {
                // Fallback for old coupons without type (assume percentage)
                $discount = ($cartTotal * $coupon->discount) / 100;
            }

            // Calculate new total
            $newTotal = $cartTotal - $discount;

            // Validation: If total becomes less than 0, don't apply coupon
            if ($newTotal < 0) {
                return response()->json([
                    'status' => false,
                    'message' => 'Coupon discount amount exceeds cart total. Cannot apply this coupon.'
                ]);
            }

            // Store coupon in session
            session()->put('applied_coupon', [
                'code' => $couponCode,
                'type' => $coupon->type,
                'discount_value' => $coupon->discount,
                'discount_amount' => $discount,
                'original_total' => $cartTotal,
                'new_total' => $newTotal
            ]);

            // Create appropriate message based on type
            $message = 'Coupon applied successfully!';
            if ($coupon->type === 'percentage') {
                $message = "Coupon applied! You saved {$coupon->discount}%";
            } elseif ($coupon->type === 'amount') {
                $message = "Coupon applied! You saved $" . number_format($coupon->discount, 2);
            }

            return response()->json([
                'status' => true,
                'message' => $message,
                'discount_amount' => $discount,
                'new_total' => $newTotal,
                'original_total' => $cartTotal
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'An error occurred while validating the coupon: ' . $e->getMessage()
            ], 500);
        }
    }

    public function notification()
    {
        $user = auth()->user();
        $notifications = Notification::where('user_id', $user->id)->with(['user', 'senderUser.profile'])->latest()->get();
        return view('dashboard.notification', compact('notifications'));
    }

    public function getUnreadNotificationCount()
    {
        $user = auth()->user();
        $count = Notification::where('user_id', $user->id)
            ->where('read', 0)
            ->count();

        return response()->json([
            'success' => true,
            'count' => $count
        ]);
    }

    public function markAllAsRead()
    {
        $user = auth()->user();
        Notification::where('user_id', $user->id)
            ->where('read', 0)
            ->update(['read' => 1]);

        return response()->json([
            'success' => true,
            'message' => 'All notifications marked as read'
        ]);
    }

    public function markAsRead($id)
    {
        $user = auth()->user();
        $notification = Notification::where('user_id', $user->id)->where('id', $id)->first();

        if ($notification) {
            $notification->read = 1;
            $notification->save();

            return response()->json([
                'success' => true,
                'message' => 'Notification marked as read'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Notification not found'
        ]);
    }
    //    business controller
    public function businessAnalytics(Request $request)
    {
        $user = auth()->user();
        Log::info("User");
        $userId = $user->id;
        $period = $request->get('period', 'weekly'); // Default to weekly

        $userRoles = $user->roles->pluck('name')->toArray();
        Log::info("=== BUSINESS ANALYTICS REQUEST START ===", [
            'user_id' => $userId,
            'user_email' => $user->email,
            'user_roles' => $userRoles,
            'period' => $period
        ]);

        // Calculate analytics data based on period
        if ($period === 'monthly') {
            Log::info("Processing MONTHLY analytics data");
            // Monthly data (current month by weeks)
            $data = $this->getMonthlyAnalyticsData($userId);
        } else {
            Log::info("Processing WEEKLY analytics data");
            // Weekly data (last 7 days)
            $data = $this->getWeeklyAnalyticsData($userId);
        }

        $data['selectedPeriod'] = $period;

        Log::info("=== BUSINESS ANALYTICS REQUEST COMPLETE ===", [
            'data_keys' => array_keys($data),
            'daily_revenue' => $data['dailyRevenueData']['dailyRevenue'] ?? 'N/A',
            'service_count' => count($data['servicePerformance'] ?? [])
        ]);

        return view('dashboard.business.analytics', $data);
    }

    private function getWeeklyAnalyticsData($userId)
    {
        Log::info("=== WEEKLY ANALYTICS START ===", ['user_id' => $userId]);

        $user = auth()->user();
        $isBusiness = $user->hasAnyRole(['business']);
        $baseQuery = Booking::query();

        if ($isBusiness) {
            Log::info("Processing as BUSINESS user");

            $staffIds = $user->staffs()->pluck('id')->toArray();
            Log::info("Staff IDs", ['staff_ids' => $staffIds]);

            $baseQuery->where(function ($query) use ($userId, $staffIds) {
                $query->where('provider_id', $userId)
                    ->orWhereHas('bookingServices', function ($sub) use ($staffIds) {
                        $sub->whereIn('staff_id', $staffIds);
                    });
            });
        } else {
            Log::info("Processing as INDIVIDUAL user");
            $baseQuery->where('provider_id', $userId);
        }

        $today = now();
        $startDate = $today->copy()->subDays(6)->startOfDay();
        $endDate = $today->copy()->endOfDay();

        $labels = [];
        $dailyBookings = [];
        $dailyUpcoming = [];
        $dailyCompleted = [];
        $dailyRevenue = [];

        $totalSales = 0;
        $totalBookings = 0;
        $upcomingCount = 0;
        $completedCount = 0;

        for ($i = 6; $i >= 0; $i--) {
            $date = $today->copy()->subDays($i)->toDateString();
            $labels[] = $today->copy()->subDays($i)->format('D');

            // Clone to prevent query mutation
            $queryForDay = clone $baseQuery;

            $dayBookings = (clone $queryForDay)->whereDate('booking_date', $date)->where('status', '!=', 2);
            $dailyBookings[] = $dayBookings->count();

            $dayUpcoming = (clone $queryForDay)->whereDate('booking_date', $date)->where('status', 0);
            $dailyUpcoming[] = $dayUpcoming->count();
            $upcomingCount += $dayUpcoming->count();

            $dayCompleted = (clone $queryForDay)->whereDate('booking_date', $date)->where('status', 1);
            $dailyCompleted[] = $dayCompleted->count();
            $completedCount += $dayCompleted->count();

            $dayRevenue = (clone $queryForDay)->whereDate('booking_date', $date)->where('status', 1)->sum('total_amount');

            Log::info("Weekly sales daily calculation", [
                'date' => $date,
                'day_revenue_by_booking_date' => $dayRevenue,
                'running_total_sales' => $totalSales + $dayRevenue
            ]);

            $dailyRevenue[] = $dayRevenue;
            $totalSales += $dayRevenue;
        }

        $totalBookings = (clone $baseQuery)
            ->whereBetween('booking_date', [$startDate, $endDate])
            ->where('status', '!=', 2)
            ->count();

        // Fix upcoming bookings calculation - should look for future bookings regardless of date range
        $upcomingBookingsTotal = (clone $baseQuery)
            ->where('status', 0)
            ->where('booking_date', '>=', now()->toDateString())
            ->count();

        // Fix completed bookings calculation
        $completedBookingsTotal = (clone $baseQuery)
            ->where('status', 1)
            ->count();

        Log::info("Booking counts debug", [
            'total_bookings' => $totalBookings,
            'upcoming_from_loop' => $upcomingCount,
            'upcoming_total_fixed' => $upcomingBookingsTotal,
            'completed_from_loop' => $completedCount,
            'completed_total_fixed' => $completedBookingsTotal,
            'date_range' => [$startDate, $endDate]
        ]);

        // Revenue comparison with previous week
        $lastWeekStart = now()->subDays(13)->startOfDay();
        $lastWeekEnd = now()->subDays(7)->endOfDay();

        $lastWeekRevenue = (clone $baseQuery)
            ->whereBetween('booking_date', [$lastWeekStart, $lastWeekEnd])
            ->where('status', 1)
            ->sum('total_amount');

        $salesChange = $lastWeekRevenue > 0 ? round((($totalSales - $lastWeekRevenue) / $lastWeekRevenue) * 100, 1) : 0;

        Log::info("Weekly sales calculation summary", [
            'total_sales_by_booking_date' => $totalSales,
            'last_week_revenue' => $lastWeekRevenue,
            'sales_change_percent' => $salesChange,
            'date_range_used' => 'booking_date (not created_at)'
        ]);

        // Booking count change
        $lastWeekBookings = (clone $baseQuery)
            ->whereBetween('booking_date', [$lastWeekStart, $lastWeekEnd])
            ->where('status', '!=', 2)
            ->count();

        $bookingChange = $lastWeekBookings > 0 ? round((($totalBookings - $lastWeekBookings) / $lastWeekBookings) * 100, 1) : 0;

        $lastWeekCompleted = (clone $baseQuery)
            ->where('status', 1)
            ->whereBetween('booking_date', [$lastWeekStart, $lastWeekEnd])
            ->count();

        $completedChange = $lastWeekCompleted > 0
            ? round((($completedCount - $lastWeekCompleted) / $lastWeekCompleted) * 100, 1)
            : 0;

        // Sub-metrics
        $servicePerformance = $this->getServicePerformance($userId, 'weekly');
        $dailyRevenueData = $this->getDailyRevenueBreakdown($userId, 'weekly');
        $staffPerformance = $this->getStaffPerformance($userId, 'weekly');
        $platformFinancials = $this->getPlatformFinancialOverview($userId, 'weekly');
        $userAcquisition = $this->getUserAcquisitionChurn($userId, 'weekly');
        $subscriptionAnalytics = $this->getSubscriptionPlanAnalytics($userId, 'weekly');

        return [
            'totalSales' => $totalSales,
            'salesChange' => $salesChange,
            'fromLastPeriod' => $lastWeekRevenue,
            'totalBookings' => $totalBookings,
            'bookingChange' => $bookingChange,
            'peakBookingLabels' => $labels,
            'peakBookingData' => $dailyBookings,
            'upcomingBookings' => $upcomingBookingsTotal,
            'completedBookings' => $completedBookingsTotal,
            'completedChange' => $completedChange,
            'upcomingData' => $dailyUpcoming,
            'completedData' => $dailyCompleted,
            'servicePerformance' => $servicePerformance,
            'dailyRevenueData' => $dailyRevenueData,
            'staffPerformance' => $staffPerformance,
            'platformFinancials' => $platformFinancials,
            'userAcquisition' => $userAcquisition,
            'subscriptionAnalytics' => $subscriptionAnalytics,
            'periodDescription' => 'Last 7 days'
        ];
    }


    private function getMonthlyAnalyticsData($userId)
    {
        // Get monthly data (current month by weeks)
        $monthlyLabels = [];
        $peakBookingData = [];
        $monthlyUpcoming = [];
        $monthlyCompleted = [];

        $totalSales = 0;
        $lastMonthSales = 0;
        $totalBookings = 0;

        $startOfMonth = now()->startOfMonth();
        $endOfMonth = now()->endOfMonth();

        // Group by weeks for better visualization
        for ($week = 1; $week <= 4; $week++) {
            $weekStart = $startOfMonth->copy()->addWeeks($week - 1);
            $weekEnd = $weekStart->copy()->addDays(6);

            if ($weekEnd->gt($endOfMonth)) {
                $weekEnd = $endOfMonth->copy();
            }

            $monthlyLabels[] = 'Week ' . $week;

            // Peak booking times (total bookings per week)
            $weekBookings = Booking::where('provider_id', $userId)
                ->whereBetween('booking_date', [$weekStart->toDateString(), $weekEnd->toDateString()])
                ->where('status', '!=', 2) // Exclude cancelled
                ->count();
            $peakBookingData[] = $weekBookings;

            // Upcoming bookings count
            $upcomingCount = Booking::where('provider_id', $userId)
                ->where('status', 0)
                ->whereBetween('booking_date', [$weekStart->toDateString(), $weekEnd->toDateString()])
                ->where('booking_date', '>=', now()->toDateString())
                ->count();
            $monthlyUpcoming[] = $upcomingCount;

            // Completed bookings count
            $completedCount = Booking::where('provider_id', $userId)
                ->where('status', 1)
                ->whereBetween('booking_date', [$weekStart->toDateString(), $weekEnd->toDateString()])
                ->count();
            $monthlyCompleted[] = $completedCount;

            // Sales data
            $weekSales = Booking::where('provider_id', $userId)
                ->where('status', 1)
                ->whereBetween('booking_date', [$weekStart->toDateString(), $weekEnd->toDateString()])
                ->sum('total_amount');
            $totalSales += $weekSales;
        }

        // Last month sales for comparison
        $lastMonthStart = now()->subMonth()->startOfMonth();
        $lastMonthEnd = now()->subMonth()->endOfMonth();
        $lastMonthSales = Booking::where('provider_id', $userId)
            ->where('status', 1)
            ->whereBetween('booking_date', [$lastMonthStart->toDateString(), $lastMonthEnd->toDateString()])
            ->sum('total_amount');

        // Calculate total bookings for the month
        $totalBookings = Booking::where('provider_id', $userId)
            ->whereBetween('booking_date', [$startOfMonth->toDateString(), $endOfMonth->toDateString()])
            ->where('status', '!=', 2)
            ->count();

        // Calculate percentage changes
        $salesChange = $lastMonthSales > 0 ? round((($totalSales - $lastMonthSales) / $lastMonthSales) * 100, 1) : 0;

        // Calculate booking changes
        $lastMonthTotalBookings = Booking::where('provider_id', $userId)
            ->whereBetween('booking_date', [$lastMonthStart->toDateString(), $lastMonthEnd->toDateString()])
            ->where('status', '!=', 2)
            ->count();

        $bookingChange = $lastMonthTotalBookings > 0 ? round((($totalBookings - $lastMonthTotalBookings) / $lastMonthTotalBookings) * 100, 1) : 0;

        $lastMonthCompleted = Booking::where('provider_id', $userId)
            ->where('status', 1)
            ->whereBetween('booking_date', [$lastMonthStart->toDateString(), $lastMonthEnd->toDateString()])
            ->count();

        $completedChange = $lastMonthCompleted > 0 ? round(((array_sum($monthlyCompleted) - $lastMonthCompleted) / $lastMonthCompleted) * 100, 1) : 0;

        // Get all analytics data
        $servicePerformance = $this->getServicePerformance($userId, 'monthly');
        $dailyRevenueData = $this->getDailyRevenueBreakdown($userId, 'monthly');
        $staffPerformance = $this->getStaffPerformance($userId, 'monthly');
        $platformFinancials = $this->getPlatformFinancialOverview($userId, 'monthly');
        $userAcquisition = $this->getUserAcquisitionChurn($userId, 'monthly');
        $subscriptionAnalytics = $this->getSubscriptionPlanAnalytics($userId, 'monthly');

        // Fix monthly upcoming bookings calculation
        $monthlyUpcomingTotal = Booking::where('provider_id', $userId)
            ->where('status', 0)
            ->where('booking_date', '>=', now()->toDateString())
            ->count();

        // Fix monthly completed bookings calculation
        $monthlyCompletedTotal = Booking::where('provider_id', $userId)
            ->where('status', 1)
            ->count();

        Log::info("Monthly booking counts debug", [
            'monthly_upcoming_from_loop' => array_sum($monthlyUpcoming),
            'monthly_upcoming_total_fixed' => $monthlyUpcomingTotal,
            'monthly_completed_from_loop' => array_sum($monthlyCompleted),
            'monthly_completed_total_fixed' => $monthlyCompletedTotal
        ]);

        return [
            'totalSales' => $totalSales,
            'salesChange' => $salesChange,
            'fromLastPeriod' => $lastMonthSales,
            'totalBookings' => $totalBookings,
            'bookingChange' => $bookingChange,
            'peakBookingLabels' => $monthlyLabels,
            'peakBookingData' => $peakBookingData,
            'upcomingBookings' => $monthlyUpcomingTotal,
            'completedBookings' => $monthlyCompletedTotal,
            'completedChange' => $completedChange,
            'upcomingData' => $monthlyUpcoming,
            'completedData' => $monthlyCompleted,
            'servicePerformance' => $servicePerformance,
            'dailyRevenueData' => $dailyRevenueData,
            'staffPerformance' => $staffPerformance,
            'platformFinancials' => $platformFinancials,
            'userAcquisition' => $userAcquisition,
            'subscriptionAnalytics' => $subscriptionAnalytics,
            'periodDescription' => 'This month'
        ];
    }

    private function getServicePerformance($userId, $period = 'weekly')
    {
        Log::info("=== SERVICE PERFORMANCE START ===", ['user_id' => $userId, 'period' => $period]);

        // Calculate date ranges
        if ($period === 'monthly') {
            $currentStart = now()->startOfMonth();
            $currentEnd = now()->endOfMonth();
            $previousStart = now()->subMonth()->startOfMonth();
            $previousEnd = now()->subMonth()->endOfMonth();
        } else {
            $currentStart = now()->subDays(6)->startOfDay();
            $currentEnd = now()->endOfDay();
            $previousStart = now()->subDays(13)->startOfDay();
            $previousEnd = now()->subDays(7)->endOfDay();
        }

        Log::info("Service performance date ranges", [
            'current_start' => $currentStart->toDateTimeString(),
            'current_end' => $currentEnd->toDateTimeString(),
            'previous_start' => $previousStart->toDateTimeString(),
            'previous_end' => $previousEnd->toDateTimeString()
        ]);

        // Get user to check if they are business or individual
        $user = auth()->user();
        $userRoles = $user->roles->pluck('name')->toArray();
        Log::info("User roles for service performance", ['roles' => $userRoles]);

        // Build base query for bookings
        $baseBookingQuery = Booking::where('status', '!=', 2);
        $baseRevenueQuery = Booking::where('status', 1);

        // For business users, include bookings from all their staff
        if ($user->hasAnyRole(['business'])) {
            // Get all staff IDs for this business user
            $staffIds = $user->staffs()->pluck('id')->toArray();

            // Include bookings where provider_id is the business owner OR where staff performed the service
            $baseBookingQuery->where(function ($query) use ($userId, $staffIds) {
                $query->where('provider_id', $userId)
                    ->orWhereHas('bookingServices', function ($subQuery) use ($staffIds) {
                        $subQuery->whereIn('staff_id', $staffIds);
                    });
            });

            $baseRevenueQuery->where(function ($query) use ($userId, $staffIds) {
                $query->where('provider_id', $userId)
                    ->orWhereHas('bookingServices', function ($subQuery) use ($staffIds) {
                        $subQuery->whereIn('staff_id', $staffIds);
                    });
            });
        } else {
            // For individual users, only their own bookings
            $baseBookingQuery->where('provider_id', $userId);
            $baseRevenueQuery->where('provider_id', $userId);
        }

        // Get services with their performance data
        $services = Service::where('user_id', $userId)->get();
        Log::info("Services found for user", ['service_count' => $services->count(), 'service_names' => $services->pluck('name')->toArray()]);

        $servicePerformance = [];

        foreach ($services as $service) {
            Log::info("Processing service performance", ['service_id' => $service->id, 'service_name' => $service->name]);
            // Current period bookings
            $currentBookings = (clone $baseBookingQuery)
                ->where('service_id', $service->id)
                ->whereBetween('created_at', [$currentStart, $currentEnd])
                ->count();

            // Previous period bookings
            $previousBookings = (clone $baseBookingQuery)
                ->where('service_id', $service->id)
                ->whereBetween('created_at', [$previousStart, $previousEnd])
                ->count();

            // Current period revenue
            $currentRevenue = (clone $baseRevenueQuery)
                ->where('service_id', $service->id)
                ->whereBetween('created_at', [$currentStart, $currentEnd])
                ->sum('total_amount');

            // Previous period revenue
            $previousRevenue = (clone $baseRevenueQuery)
                ->where('service_id', $service->id)
                ->whereBetween('created_at', [$previousStart, $previousEnd])
                ->sum('total_amount');

            // Debug: Check revenue bookings for this service
            $revenueBookings = Booking::where('provider_id', $userId)
                ->where('service_id', $service->id)
                ->where('status', 1)
                ->whereBetween('created_at', [$currentStart, $currentEnd])
                ->get(['id', 'total_amount', 'service_price', 'service_price', 'status', 'created_at']);

            Log::info("Service revenue bookings debug", [
                'service_id' => $service->id,
                'service_name' => $service->name,
                'revenue_bookings_count' => $revenueBookings->count(),
                'revenue_bookings_detail' => $revenueBookings->map(function ($booking) {
                    return [
                        'id' => $booking->id,
                        'total_amount' => $booking->total_amount,
                        'service_price' => $booking->service_price,
                        'price' => $booking->service_price,
                        'status' => $booking->status,
                        'created_at' => $booking->created_at
                    ];
                })->toArray(),
                'calculated_revenue' => $currentRevenue
            ]);

            // Calculate changes
            $bookingsChange = $previousBookings > 0 ? round((($currentBookings - $previousBookings) / $previousBookings) * 100, 1) : 0;
            $revenueChange = $previousRevenue > 0 ? round((($currentRevenue - $previousRevenue) / $previousRevenue) * 100, 1) : 0;

            // Average ticket value
            $avgTicketValue = $currentBookings > 0 ? $currentRevenue / $currentBookings : 0;
            $previousAvgTicket = $previousBookings > 0 ? $previousRevenue / $previousBookings : 0;
            $avgTicketChange = $previousAvgTicket > 0 ? round((($avgTicketValue - $previousAvgTicket) / $previousAvgTicket) * 100, 1) : 0;

            // Services Performed (completed bookings for this service)
            $servicesPerformed = (clone $baseRevenueQuery)
                ->where('service_id', $service->id)
                ->whereBetween('created_at', [$currentStart, $currentEnd])
                ->count();

            // Client Feedback (reviews for bookings of this service)
            $clientFeedbackCount = DB::table('reviews')
                ->join('bookings', 'reviews.booking_id', '=', 'bookings.id')
                ->where('bookings.service_id', $service->id)
                ->where('bookings.provider_id', $userId)
                ->whereBetween('reviews.created_at', [$currentStart, $currentEnd])
                ->count();

            // Hours Worked (total duration of completed bookings for this service)
            $hoursWorked = (clone $baseRevenueQuery)
                ->where('service_id', $service->id)
                ->whereBetween('created_at', [$currentStart, $currentEnd])
                ->sum('duration'); // Duration is in minutes
            $hoursWorked = round($hoursWorked / 60, 2); // Convert to hours

            // Service/Retail Revenue (assuming all revenue is service revenue for now)
            $serviceRevenue = $currentRevenue;
            $retailRevenue = 0; // Can be calculated if you have retail products

            // Total Pay (same as current revenue for completed bookings)
            $totalPay = $currentRevenue;

            // Utilization Rate calculation
            // Assuming 8 hours per day as available working hours
            $daysInPeriod = $currentStart->diffInDays($currentEnd) + 1;
            $availableHours = $daysInPeriod * 8; // 8 hours per day
            $utilizationRate = $availableHours > 0 ? round(($hoursWorked / $availableHours) * 100, 1) : 0;

            // Rebooking rate (customers who booked this service more than once)
            $uniqueCustomers = (clone $baseBookingQuery)
                ->where('service_id', $service->id)
                ->whereBetween('created_at', [$currentStart, $currentEnd])
                ->distinct('user_id')
                ->count();

            $rebookingRate = $currentBookings > 0 && $uniqueCustomers > 0 ? round((($currentBookings - $uniqueCustomers) / $currentBookings) * 100, 1) : 0;

            $performanceData = [
                'name' => $service->name,
                'category' => $service->category ?? 'General',
                'bookings' => $currentBookings,
                'bookings_change' => $bookingsChange,
                'revenue' => $currentRevenue,
                'revenue_change' => $revenueChange,
                'avg_ticket_value' => $avgTicketValue,
                'avg_ticket_change' => $avgTicketChange,
                'services_performed' => $servicesPerformed,
                'client_feedback' => $clientFeedbackCount,
                'utilization_rate' => $utilizationRate,
                'hours_worked' => $hoursWorked,
                'service_revenue' => $serviceRevenue,
                'retail_revenue' => $retailRevenue,
                'total_pay' => $totalPay,
                'rebooking_rate' => $rebookingRate,
                'rebooking_change' => 0, // Can be calculated if needed
                'duration' => $service->duration ?? 60,
                'duration_change' => 0 // Static for now
            ];

            Log::info("Service performance data", [
                'service_name' => $service->name,
                'current_bookings' => $currentBookings,
                'previous_bookings' => $previousBookings,
                'current_revenue' => $currentRevenue,
                'previous_revenue' => $previousRevenue,
                'services_performed' => $servicesPerformed,
                'client_feedback_count' => $clientFeedbackCount,
                'utilization_rate' => $utilizationRate,
                'hours_worked' => $hoursWorked,
                'service_revenue' => $serviceRevenue,
                'retail_revenue' => $retailRevenue,
                'total_pay' => $totalPay,
                'unique_customers' => $uniqueCustomers,
                'rebooking_rate' => $rebookingRate
            ]);

            $servicePerformance[] = $performanceData;
        }

        Log::info("=== SERVICE PERFORMANCE RESULT ===", [
            'total_services' => count($servicePerformance),
            'total_revenue' => array_sum(array_column($servicePerformance, 'revenue')),
            'total_bookings' => array_sum(array_column($servicePerformance, 'bookings'))
        ]);

        return $servicePerformance;
    }

    private function getDailyRevenueBreakdown($userId, $period = 'weekly')
    {
        Log::info("=== DAILY REVENUE BREAKDOWN START ===", ['user_id' => $userId, 'period' => $period]);

        // Today's revenue calculation
        $todayStart = now()->startOfDay();
        $todayEnd = now()->endOfDay();
        $yesterdayStart = now()->subDay()->startOfDay();
        $yesterdayEnd = now()->subDay()->endOfDay();

        Log::info("Date ranges", [
            'today_start' => $todayStart->toDateTimeString(),
            'today_end' => $todayEnd->toDateTimeString(),
            'yesterday_start' => $yesterdayStart->toDateTimeString(),
            'yesterday_end' => $yesterdayEnd->toDateTimeString()
        ]);

        // Get user to check if they are business or individual
        $user = auth()->user();
        $userRoles = $user->roles->pluck('name')->toArray();
        Log::info("User roles for daily revenue", ['roles' => $userRoles]);

        // Build base query for revenue calculation
        $baseQuery = Booking::where('status', 1);

        // For business users, include revenue from all their staff
        if ($user->hasAnyRole(['business'])) {
            Log::info("Processing daily revenue as BUSINESS user");

            // Get all staff IDs for this business user
            $staffIds = $user->staffs()->pluck('id')->toArray();
            Log::info("Staff IDs for daily revenue", ['staff_ids' => $staffIds, 'staff_count' => count($staffIds)]);

            // Include bookings where provider_id is the business owner OR where staff performed the service
            $baseQuery->where(function ($query) use ($userId, $staffIds) {
                $query->where('provider_id', $userId)
                    ->orWhereHas('bookingServices', function ($subQuery) use ($staffIds) {
                        $subQuery->whereIn('staff_id', $staffIds);
                    });
            });

            // Log revenue sources
            $directRevenue = Booking::where('status', 1)->where('provider_id', $userId)->sum('total_amount');
            $staffRevenue = Booking::where('status', 1)
                ->whereHas('bookingServices', function ($subQuery) use ($staffIds) {
                    $subQuery->whereIn('staff_id', $staffIds);
                })->sum('total_amount');

            Log::info("Business revenue sources", [
                'direct_revenue' => $directRevenue,
                'staff_revenue' => $staffRevenue,
                'total_revenue' => $directRevenue + $staffRevenue
            ]);
        } else {
            Log::info("Processing daily revenue as INDIVIDUAL user");

            // For individual users, only their own bookings
            $baseQuery->where('provider_id', $userId);

            // Debug: Check all bookings for this user
            $allUserBookings = Booking::where('provider_id', $userId)->get(['id', 'status', 'total_amount', 'service_price', 'created_at', 'booking_date']);
            Log::info("All bookings for user", [
                'user_id' => $userId,
                'total_bookings' => $allUserBookings->count(),
                'bookings_detail' => $allUserBookings->map(function ($booking) {
                    return [
                        'id' => $booking->id,
                        'status' => $booking->status,
                        'total_amount' => $booking->total_amount,
                        'service_price' => $booking->service_price,
                        'price' => $booking->service_price,
                        'created_at' => $booking->created_at,
                        'booking_date' => $booking->booking_date
                    ];
                })->toArray()
            ]);

            // Check completed bookings specifically
            $completedBookings = Booking::where('provider_id', $userId)->where('status', 1)->get(['id', 'total_amount', 'service_price', 'service_price']);
            Log::info("Completed bookings for user", [
                'completed_count' => $completedBookings->count(),
                'completed_detail' => $completedBookings->map(function ($booking) {
                    return [
                        'id' => $booking->id,
                        'total_amount' => $booking->total_amount,
                        'service_price' => $booking->service_price,
                        'price' => $booking->service_price
                    ];
                })->toArray()
            ]);

            $individualRevenue = (clone $baseQuery)->sum('total_amount');
            Log::info("Individual total revenue", ['total_revenue' => $individualRevenue]);
        }

        // Total Daily Revenue (Today only)
        $totalDailyRevenue = (clone $baseQuery)
            ->whereBetween('created_at', [$todayStart, $todayEnd])
            ->sum('total_amount');

        // Yesterday's revenue for comparison
        $previousDailyRevenue = (clone $baseQuery)
            ->whereBetween('created_at', [$yesterdayStart, $yesterdayEnd])
            ->sum('total_amount');

        $dailyRevenueChange = $previousDailyRevenue > 0
            ? round((($totalDailyRevenue - $previousDailyRevenue) / $previousDailyRevenue) * 100, 1)
            : 0;

        Log::info("Daily revenue calculations", [
            'today_revenue' => $totalDailyRevenue,
            'yesterday_revenue' => $previousDailyRevenue,
            'revenue_change_percent' => $dailyRevenueChange
        ]);

        // Today's Service Revenue
        $serviceRevenue = (clone $baseQuery)
            ->whereNotNull('service_id')
            ->whereBetween('created_at', [$todayStart, $todayEnd])
            ->sum('total_amount');

        // Yesterday's Service Revenue for comparison
        $previousServiceRevenue = (clone $baseQuery)
            ->whereNotNull('service_id')
            ->whereBetween('created_at', [$yesterdayStart, $yesterdayEnd])
            ->sum('total_amount');

        $serviceRevenueChange = $previousServiceRevenue > 0
            ? round((($serviceRevenue - $previousServiceRevenue) / $previousServiceRevenue) * 100, 1)
            : 0;

        Log::info("Service revenue calculations", [
            'today_service_revenue' => $serviceRevenue,
            'yesterday_service_revenue' => $previousServiceRevenue,
            'service_revenue_change_percent' => $serviceRevenueChange
        ]);

        // Today's Net Revenue (after platform fees, etc.)
        $platformFeeRate = 0.05; // 5% platform fee
        $netRevenue = $totalDailyRevenue * (1 - $platformFeeRate);
        $previousNetRevenue = $previousDailyRevenue * (1 - $platformFeeRate);

        $netRevenueChange = $previousNetRevenue > 0
            ? round((($netRevenue - $previousNetRevenue) / $previousNetRevenue) * 100, 1)
            : 0;

        // Today's Discounts Given (from bookings with discount coupons)
        $discountsGiven = (clone $baseQuery)
            ->whereBetween('created_at', [$todayStart, $todayEnd])
            ->whereNotNull('discount_coupon_id')
            ->sum('discount_amount');

        // Yesterday's Discounts Given for comparison
        $previousDiscountsGiven = (clone $baseQuery)
            ->whereBetween('created_at', [$yesterdayStart, $yesterdayEnd])
            ->whereNotNull('discount_coupon_id')
            ->sum('discount_amount');

        $discountsChange = $previousDiscountsGiven > 0
            ? round((($discountsGiven - $previousDiscountsGiven) / $previousDiscountsGiven) * 100, 1)
            : 0;

        Log::info("Discount calculations", [
            'today_discounts' => $discountsGiven,
            'yesterday_discounts' => $previousDiscountsGiven,
            'discounts_change_percent' => $discountsChange
        ]);

        $result = [
            'dailyRevenue' => $totalDailyRevenue,
            'dailyRevenueChange' => $dailyRevenueChange,
            'serviceRevenue' => $serviceRevenue,
            'serviceRevenueChange' => $serviceRevenueChange,
            'netRevenue' => $netRevenue,
            'netRevenueChange' => $netRevenueChange,
            'discountsGiven' => $discountsGiven,
            'discountsChange' => $discountsChange
        ];

        Log::info("=== DAILY REVENUE BREAKDOWN RESULT ===", $result);

        return $result;
    }

    private function getStaffPerformance($userId, $period = 'weekly')
    {
        // Calculate date ranges
        if ($period === 'monthly') {
            $currentStart = now()->startOfMonth();
            $currentEnd = now()->endOfMonth();
            $previousStart = now()->subMonth()->startOfMonth();
            $previousEnd = now()->subMonth()->endOfMonth();
        } else {
            $currentStart = now()->subDays(6)->startOfDay();
            $currentEnd = now()->endOfDay();
            $previousStart = now()->subDays(13)->startOfDay();
            $previousEnd = now()->subDays(7)->endOfDay();
        }

        // Get user to check if they are business
        $user = auth()->user();
        $staffPerformance = [];

        // Only show staff performance for business users
        if ($user->hasAnyRole(['business'])) {
            Log::info("=== STAFF PERFORMANCE START === (Business User)", ['user_id' => $userId]);

            // Get all staff for this business user
            $staffMembers = Staff::where('user_id', $userId)
                ->where('status', 1) // Active staff only
                ->get();

            Log::info("Staff members found", [
                'staff_count' => $staffMembers->count(),
                'staff_names' => $staffMembers->pluck('name')->toArray()
            ]);

            foreach ($staffMembers as $staff) {
                // Get bookings performed by this staff member
                $staffBookings = DB::table('booking_service')
                    ->join('bookings', 'booking_service.booking_id', '=', 'bookings.id')
                    ->where('booking_service.staff_id', $staff->id)
                    ->where('bookings.status', 1) // Completed bookings only
                    ->whereBetween('bookings.created_at', [$currentStart, $currentEnd]);

                $currentBookings = (clone $staffBookings)->count();

                // Previous period bookings
                $previousStaffBookings = DB::table('booking_service')
                    ->join('bookings', 'booking_service.booking_id', '=', 'bookings.id')
                    ->where('booking_service.staff_id', $staff->id)
                    ->where('bookings.status', 1)
                    ->whereBetween('bookings.created_at', [$previousStart, $previousEnd])
                    ->count();

                $bookingsChange = $previousStaffBookings > 0
                    ? round((($currentBookings - $previousStaffBookings) / $previousStaffBookings) * 100, 1)
                    : 0;

                // Revenue generated by this staff member
                $staffRevenue = (clone $staffBookings)->sum('bookings.total_amount');

                $previousStaffRevenue = DB::table('booking_service')
                    ->join('bookings', 'booking_service.booking_id', '=', 'bookings.id')
                    ->where('booking_service.staff_id', $staff->id)
                    ->where('bookings.status', 1)
                    ->whereBetween('bookings.created_at', [$previousStart, $previousEnd])
                    ->sum('bookings.total_amount');

                $revenueChange = $previousStaffRevenue > 0
                    ? round((($staffRevenue - $previousStaffRevenue) / $previousStaffRevenue) * 100, 1)
                    : 0;

                // Hours worked by this staff member
                $hoursWorked = (clone $staffBookings)->sum('bookings.duration');
                $hoursWorked = round($hoursWorked / 60, 2); // Convert to hours

                // Services performed
                $servicesPerformed = $currentBookings;

                // Client feedback for this staff member
                $clientFeedback = DB::table('reviews')
                    ->join('bookings', 'reviews.booking_id', '=', 'bookings.id')
                    ->join('booking_service', 'bookings.id', '=', 'booking_service.booking_id')
                    ->where('booking_service.staff_id', $staff->id)
                    ->whereBetween('reviews.created_at', [$currentStart, $currentEnd])
                    ->count();

                // Average rating for this staff member
                $averageRating = DB::table('reviews')
                    ->join('bookings', 'reviews.booking_id', '=', 'bookings.id')
                    ->join('booking_service', 'bookings.id', '=', 'booking_service.booking_id')
                    ->where('booking_service.staff_id', $staff->id)
                    ->whereBetween('reviews.created_at', [$currentStart, $currentEnd])
                    ->avg('reviews.average_rating') ?? 0;

                $averageRating = round($averageRating, 1);

                // Utilization rate for this staff member
                $daysInPeriod = $currentStart->diffInDays($currentEnd) + 1;
                $availableHours = $daysInPeriod * 8; // 8 hours per day
                $utilizationRate = $availableHours > 0 ? round(($hoursWorked / $availableHours) * 100, 1) : 0;

                Log::info("Staff performance data", [
                    'staff_name' => $staff->name,
                    'staff_id' => $staff->id,
                    'bookings' => $currentBookings,
                    'revenue' => $staffRevenue,
                    'hours_worked' => $hoursWorked,
                    'services_performed' => $servicesPerformed,
                    'client_feedback' => $clientFeedback,
                    'average_rating' => $averageRating,
                    'utilization_rate' => $utilizationRate
                ]);

                $staffPerformance[] = [
                    'name' => $staff->name,
                    'email' => $staff->email,
                    'category' => $staff->category->name ?? 'General',
                    'bookings' => $currentBookings,
                    'bookings_change' => $bookingsChange,
                    'revenue' => $staffRevenue,
                    'revenue_change' => $revenueChange,
                    'services_performed' => $servicesPerformed,
                    'client_feedback' => $clientFeedback,
                    'average_rating' => $averageRating,
                    'hours_worked' => $hoursWorked,
                    'utilization_rate' => $utilizationRate,
                    'status' => $staff->status == 1 ? 'Active' : 'Inactive'
                ];
            }

            Log::info("=== STAFF PERFORMANCE RESULT ===", [
                'total_staff' => count($staffPerformance),
                'total_staff_revenue' => array_sum(array_column($staffPerformance, 'revenue')),
                'total_staff_bookings' => array_sum(array_column($staffPerformance, 'bookings'))
            ]);
        } else {
            Log::info("Staff performance skipped - User is not business role", [
                'user_roles' => $user->getRoleNames()->toArray()
            ]);
        }

        return $staffPerformance;
    }

    private function getPlatformFinancialOverview($userId, $period = 'weekly')
    {
        // Calculate date ranges
        if ($period === 'monthly') {
            $currentStart = now()->startOfMonth();
            $currentEnd = now()->endOfMonth();
            $previousStart = now()->subMonth()->startOfMonth();
            $previousEnd = now()->subMonth()->endOfMonth();
        } else {
            $currentStart = now()->subDays(6)->startOfDay();
            $currentEnd = now()->endOfDay();
            $previousStart = now()->subDays(13)->startOfDay();
            $previousEnd = now()->subDays(7)->endOfDay();
        }

        // Platform fees collected
        $platformFeeRate = 0.05; // 5%
        $totalRevenue = Booking::where('provider_id', $userId)
            ->where('status', 1)
            ->whereBetween('created_at', [$currentStart, $currentEnd])
            ->sum('total_amount');

        $platformFees = $totalRevenue * $platformFeeRate;

        $previousRevenue = Booking::where('provider_id', $userId)
            ->where('status', 1)
            ->whereBetween('created_at', [$previousStart, $previousEnd])
            ->sum('total_amount');

        $previousPlatformFees = $previousRevenue * $platformFeeRate;
        $platformFeesChange = $previousPlatformFees > 0
            ? round((($platformFees - $previousPlatformFees) / $previousPlatformFees) * 100, 1)
            : 0;

        // Transaction volume
        $transactionVolume = Booking::where('provider_id', $userId)
            ->whereBetween('created_at', [$currentStart, $currentEnd])
            ->where('status', '!=', 2)
            ->count();

        $previousTransactionVolume = Booking::where('provider_id', $userId)
            ->whereBetween('created_at', [$previousStart, $previousEnd])
            ->where('status', '!=', 2)
            ->count();

        $transactionVolumeChange = $previousTransactionVolume > 0
            ? round((($transactionVolume - $previousTransactionVolume) / $previousTransactionVolume) * 100, 1)
            : 0;

        // Average transaction value
        $avgTransactionValue = $transactionVolume > 0 ? $totalRevenue / $transactionVolume : 0;
        $previousAvgTransactionValue = $previousTransactionVolume > 0 ? $previousRevenue / $previousTransactionVolume : 0;
        $avgTransactionValueChange = $previousAvgTransactionValue > 0
            ? round((($avgTransactionValue - $previousAvgTransactionValue) / $previousAvgTransactionValue) * 100, 1)
            : 0;

        return [
            'platform_fees' => $platformFees,
            'platform_fees_change' => $platformFeesChange,
            'transaction_volume' => $transactionVolume,
            'transaction_volume_change' => $transactionVolumeChange,
            'avg_transaction_value' => $avgTransactionValue,
            'avg_transaction_value_change' => $avgTransactionValueChange,
            'total_revenue' => $totalRevenue,
            'revenue_change' => $previousRevenue > 0 ? round((($totalRevenue - $previousRevenue) / $previousRevenue) * 100, 1) : 0
        ];
    }

    private function getUserAcquisitionChurn($userId, $period = 'weekly')
    {
        // Calculate date ranges
        if ($period === 'monthly') {
            $currentStart = now()->startOfMonth();
            $currentEnd = now()->endOfMonth();
            $previousStart = now()->subMonth()->startOfMonth();
            $previousEnd = now()->subMonth()->endOfMonth();
        } else {
            $currentStart = now()->subDays(6)->startOfDay();
            $currentEnd = now()->endOfDay();
            $previousStart = now()->subDays(13)->startOfDay();
            $previousEnd = now()->subDays(7)->endOfDay();
        }

        // New users (customers who made their first booking)
        $newUsers = Booking::where('provider_id', $userId)
            ->whereBetween('created_at', [$currentStart, $currentEnd])
            ->distinct('user_id')
            ->count();

        $previousNewUsers = Booking::where('provider_id', $userId)
            ->whereBetween('created_at', [$previousStart, $previousEnd])
            ->distinct('user_id')
            ->count();

        $newUsersChange = $previousNewUsers > 0
            ? round((($newUsers - $previousNewUsers) / $previousNewUsers) * 100, 1)
            : 0;

        // Active users (users who made bookings in current period)
        $activeUsers = Booking::where('provider_id', $userId)
            ->whereBetween('created_at', [$currentStart, $currentEnd])
            ->where('status', '!=', 2)
            ->distinct('user_id')
            ->count();

        $previousActiveUsers = Booking::where('provider_id', $userId)
            ->whereBetween('created_at', [$previousStart, $previousEnd])
            ->where('status', '!=', 2)
            ->distinct('user_id')
            ->count();

        $activeUsersChange = $previousActiveUsers > 0
            ? round((($activeUsers - $previousActiveUsers) / $previousActiveUsers) * 100, 1)
            : 0;

        // Churn rate (users who cancelled bookings)
        $churnedUsers = Booking::where('provider_id', $userId)
            ->where('status', 2)
            ->whereBetween('created_at', [$currentStart, $currentEnd])
            ->distinct('user_id')
            ->count();

        $churnRate = $activeUsers > 0 ? round(($churnedUsers / $activeUsers) * 100, 1) : 0;

        $previousChurnedUsers = Booking::where('provider_id', $userId)
            ->where('status', 2)
            ->whereBetween('created_at', [$previousStart, $previousEnd])
            ->distinct('user_id')
            ->count();

        $previousChurnRate = $previousActiveUsers > 0 ? round(($previousChurnedUsers / $previousActiveUsers) * 100, 1) : 0;
        $churnRateChange = $previousChurnRate > 0
            ? round((($churnRate - $previousChurnRate) / $previousChurnRate) * 100, 1)
            : 0;

        // Retention rate
        $retentionRate = 100 - $churnRate;
        $retentionRateChange = -$churnRateChange; // Inverse of churn rate change

        return [
            'new_users' => $newUsers,
            'new_users_change' => $newUsersChange,
            'active_users' => $activeUsers,
            'active_users_change' => $activeUsersChange,
            'churn_rate' => $churnRate,
            'churn_rate_change' => $churnRateChange,
            'retention_rate' => $retentionRate,
            'retention_rate_change' => $retentionRateChange
        ];
    }

    private function getSubscriptionPlanAnalytics($userId, $period = 'weekly')
    {
        // Calculate date ranges
        if ($period === 'monthly') {
            $currentStart = now()->startOfMonth();
            $currentEnd = now()->endOfMonth();
            $previousStart = now()->subMonth()->startOfMonth();
            $previousEnd = now()->subMonth()->endOfMonth();
        } else {
            $currentStart = now()->subDays(6)->startOfDay();
            $currentEnd = now()->endOfDay();
            $previousStart = now()->subDays(13)->startOfDay();
            $previousEnd = now()->subDays(7)->endOfDay();
        }

        // Active subscriptions (simulate with recurring bookings)
        $activeSubscriptions = Booking::where('provider_id', $userId)
            ->whereBetween('created_at', [$currentStart, $currentEnd])
            ->where('status', '!=', 2)
            ->distinct('user_id')
            ->count();

        $previousActiveSubscriptions = Booking::where('provider_id', $userId)
            ->whereBetween('created_at', [$previousStart, $previousEnd])
            ->where('status', '!=', 2)
            ->distinct('user_id')
            ->count();

        $subscriptionChange = $previousActiveSubscriptions > 0
            ? round((($activeSubscriptions - $previousActiveSubscriptions) / $previousActiveSubscriptions) * 100, 1)
            : 0;

        // Monthly Recurring Revenue
        $monthlyRecurringRevenue = Booking::where('provider_id', $userId)
            ->where('status', 1)
            ->whereBetween('created_at', [$currentStart, $currentEnd])
            ->sum('total_amount');

        $previousMRR = Booking::where('provider_id', $userId)
            ->where('status', 1)
            ->whereBetween('created_at', [$previousStart, $previousEnd])
            ->sum('total_amount');

        $mrrChange = $previousMRR > 0
            ? round((($monthlyRecurringRevenue - $previousMRR) / $previousMRR) * 100, 1)
            : 0;

        // Plan upgrades (high-value bookings)
        $planUpgrades = Booking::where('provider_id', $userId)
            ->whereBetween('created_at', [$currentStart, $currentEnd])
            ->where('total_amount', '>', 100)
            ->count();

        $previousUpgrades = Booking::where('provider_id', $userId)
            ->whereBetween('created_at', [$previousStart, $previousEnd])
            ->where('total_amount', '>', 100)
            ->count();

        $upgradeChange = $previousUpgrades > 0
            ? round((($planUpgrades - $previousUpgrades) / $previousUpgrades) * 100, 1)
            : 0;

        // Churn rate
        $churnedSubscriptions = Booking::where('provider_id', $userId)
            ->where('status', 2)
            ->whereBetween('created_at', [$currentStart, $currentEnd])
            ->distinct('user_id')
            ->count();

        $churnRate = $activeSubscriptions > 0 ? round(($churnedSubscriptions / $activeSubscriptions) * 100, 1) : 0;

        $previousChurned = Booking::where('provider_id', $userId)
            ->where('status', 2)
            ->whereBetween('created_at', [$previousStart, $previousEnd])
            ->distinct('user_id')
            ->count();

        $previousChurnRate = $previousActiveSubscriptions > 0 ? round(($previousChurned / $previousActiveSubscriptions) * 100, 1) : 0;
        $churnChange = $previousChurnRate > 0
            ? round((($churnRate - $previousChurnRate) / $previousChurnRate) * 100, 1)
            : 0;

        return [
            'activeSubscriptions' => $activeSubscriptions,
            'subscriptionChange' => $subscriptionChange,
            'monthlyRecurringRevenue' => $monthlyRecurringRevenue,
            'mrrChange' => $mrrChange,
            'planUpgrades' => $planUpgrades,
            'upgradeChange' => $upgradeChange,
            'churnRate' => $churnRate,
            'churnChange' => $churnChange
        ];
    }

    public function businessEarning()
    {
        $baseQuery = Booking::query();

        // Apply role-based filtering
        if (auth()->user()->hasAnyRole(['individual', 'professional', 'business'])) {
            $baseQuery->where('provider_id', auth()->id());
        }

        // Total Revenue (completed bookings)
        $totalRevenue = (clone $baseQuery)
            ->where('status', 1)
            ->sum('total_amount');

        // Pending Amount (upcoming bookings - status 0)
        $pendingAmount = (clone $baseQuery)
            ->where('status', 0)
            ->sum('total_amount');

        // Completed Bookings Count
        $completedBookingsCount = (clone $baseQuery)
            ->where('status', 1)
            ->count();

        // Get only completed bookings for the table with relationships
        $bookings = (clone $baseQuery)
            ->where('status', 1)
            ->with(['service', 'service.category', 'customer'])
            ->orderBy('created_at', 'desc')
            ->get();

        // Get categories for the filter dropdown
        $categories = \App\Models\Category::active()->get();

        // Calculate percentage changes (mock data for now)
        $earningsStats = [
            'total_revenue' => $totalRevenue,
            'total_revenue_change' => 17.2, // Mock percentage
            'pending_amount' => $pendingAmount,
            'pending_amount_change' => 12.5, // Mock percentage
            'completed_bookings_count' => $completedBookingsCount,
            'completed_bookings_change' => 8.3, // Mock percentage
        ];

        return view('dashboard.business.earning', compact('bookings', 'earningsStats', 'categories'));
    }

    public function filterEarnings(Request $request)
    {
        $baseQuery = Booking::query();

        // Apply role-based filtering
        if (auth()->user()->hasAnyRole(['individual', 'professional', 'business'])) {
            $baseQuery->where('provider_id', auth()->id());
        }

        // Only show completed bookings for earning page
        $query = (clone $baseQuery)->where('status', 1)->with(['service', 'service.category', 'customer']);

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->whereHas('customer', function ($customerQuery) use ($search) {
                    $customerQuery->where('name', 'like', "%{$search}%");
                })
                    ->orWhereHas('service', function ($serviceQuery) use ($search) {
                        $serviceQuery->where('name', 'like', "%{$search}%");
                    })
                    ->orWhereHas('service.category', function ($categoryQuery) use ($search) {
                        $categoryQuery->where('name', 'like', "%{$search}%");
                    });
            });
        }

        // Status filter removed since we only show completed bookings

        // Apply category filter
        if ($request->filled('category') && $request->get('category') !== 'Category') {
            $query->whereHas('service.category', function ($categoryQuery) use ($request) {
                $categoryQuery->where('name', $request->get('category'));
            });
        }

        $bookings = $query->orderBy('created_at', 'desc')->get();

        // Generate HTML for the table rows
        $html = view('dashboard.business.partials.earning-table-rows', compact('bookings'))->render();

        return response()->json([
            'success' => true,
            'html' => $html,
            'count' => $bookings->count()
        ]);
    }

    public function exportEarnings(Request $request)
    {
        $baseQuery = Booking::query();

        // Apply role-based filtering
        if (auth()->user()->hasAnyRole(['individual', 'professional', 'business'])) {
            $baseQuery->where('provider_id', auth()->id());
        }

        // Only export completed bookings for earning page
        $query = (clone $baseQuery)->where('status', 1)->with(['service', 'service.category', 'customer']);

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->whereHas('customer', function ($customerQuery) use ($search) {
                    $customerQuery->where('name', 'like', "%{$search}%");
                })
                    ->orWhereHas('service', function ($serviceQuery) use ($search) {
                        $serviceQuery->where('name', 'like', "%{$search}%");
                    })
                    ->orWhereHas('service.category', function ($categoryQuery) use ($search) {
                        $categoryQuery->where('name', 'like', "%{$search}%");
                    });
            });
        }

        // Apply category filter
        if ($request->filled('category') && $request->get('category') !== 'Category') {
            $query->whereHas('service.category', function ($categoryQuery) use ($request) {
                $categoryQuery->where('name', $request->get('category'));
            });
        }

        $bookings = $query->orderBy('created_at', 'desc')->get();

        // Create filename with filter info
        $filename = 'earnings-' . now()->format('Y-m-d');
        if ($request->filled('status') && $request->get('status') !== 'all') {
            $filename .= '-' . $request->get('status');
        }
        if ($request->filled('category') && $request->get('category') !== 'Category') {
            $filename .= '-' . str_replace(' ', '-', strtolower($request->get('category')));
        }
        $filename .= '.xlsx';

        return \Excel::download(new \App\Exports\EarningsExport($bookings), $filename);
    }

    public function staffMemberDetails()
    {
        return view('dashboard.business.staff-member-details');
    }
    // Individual

    // admin

    public function refundRequest(Request $request)
    {
        $query = Booking::with(['user', 'service', 'provider'])
            ->whereIn('status', [2, 3, 4]); // Status 2 = pending refund, Status 3 = refunded

        // Search filter
        if ($request->has('search')) {
            $search = $request->get('search');
            if ($search !== '') {
                $query->where(function ($q) use ($search) {
                    $q->whereHas('user', function ($userQuery) use ($search) {
                        $userQuery->where('name', 'like', "%{$search}%")
                            ->orWhere('email', 'like', "%{$search}%");
                    })
                        ->orWhereHas('service', function ($serviceQuery) use ($search) {
                            $serviceQuery->where('name', 'like', "%{$search}%");
                        })
                        ->orWhereHas('provider', function ($providerQuery) use ($search) {
                            $providerQuery->where('name', 'like', "%{$search}%");
                        })
                        ->orWhere('booking_number', 'like', "%{$search}%");
                });
            }
        }
        // Status filter
        if ($request->filled('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }
        $refunds = $query->latest()->get();
        // If it's an AJAX request, return JSON
        if ($request->ajax()) {
            return response()->json([
                'html' => view('dashboard.admin.partials.refund-table-rows', compact('refunds'))->render()
            ]);
        }
        return view('dashboard.admin.refund-request', compact('refunds'));
    }

    public function updateRefundStatus(Request $request)
    {
        try {
            $booking = Booking::with(['service', 'customer', 'provider'])->findOrFail($request->booking_id);
            $booking->status = $request->status;
            $booking->save();

            // Get status text for notifications
            $statusText = $this->getStatusText($request->status);
            $serviceName = $booking->service->name ?? 'Service';
            $bookingNumber = $booking->booking_number ?? 'N/A';

            // Send notification to customer (user who made the booking)
            if ($booking->user_id) {
                $customerTitle = 'Booking Status Updated';
                $customerMessage = "Your booking #{$bookingNumber} for {$serviceName} has been {$statusText}.";
                $this->user_notification(
                    $booking->user_id,
                    $customerTitle,
                    $customerMessage,
                    auth()->id(),
                    'booking_status'
                );
            }

            // Send notification to provider (service provider)
            if ($booking->provider_id && $booking->provider_id != $booking->user_id) {
                $providerTitle = 'Booking Status Updated';
                $providerMessage = "Booking #{$bookingNumber} for your service {$serviceName} has been {$statusText}.";
                $this->user_notification(
                    $booking->provider_id,
                    $providerTitle,
                    $providerMessage,
                    auth()->id(),
                    'booking_status'
                );
            }

            return response()->json([
                'success' => true,
                'message' => "Refund status updated successfully. Notifications sent to customer and provider."
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating refund status: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get human-readable status text
     */
    private function getStatusText($status)
    {
        $statusMap = [
            0 => 'pending',
            1 => 'confirmed',
            2 => 'completed',
            3 => 'cancelled',
            4 => 'refunded',
            5 => 'rejected'
        ];

        return $statusMap[$status] ?? 'updated';
    }

    public function refundDetail(Request $request, $id)
    {
        try {
            $refund = Booking::with(['user', 'service', 'provider', 'customer'])
                ->whereIn('status', [2, 3, 4]) // Status 2 = pending refund, Status 3 = refunded, Status 4 = denied
                ->findOrFail($id);

            return view('dashboard.admin.refund-detail', compact('refund'));
        } catch (\Exception $e) {
            return redirect()->route('refund_request')->with([
                'title' => 'Error',
                'message' => 'Refund request not found.',
                'type' => 'error'
            ]);
        }
    }

    public function exportRefundRequests(Request $request)
    {
        try {
            $query = Booking::with(['user', 'service', 'provider'])
                ->whereIn('status', [2, 3, 4]); // Status 2 = pending refund, Status 3 = refunded, Status 4 = denied

            // Apply same filters as in refundRequest method
            if ($request->has('search')) {
                $search = $request->get('search');
                if ($search !== '') {
                    $query->where(function ($q) use ($search) {
                        $q->whereHas('user', function ($userQuery) use ($search) {
                            $userQuery->where('name', 'like', "%{$search}%")
                                ->orWhere('email', 'like', "%{$search}%");
                        })
                            ->orWhereHas('service', function ($serviceQuery) use ($search) {
                                $serviceQuery->where('name', 'like', "%{$search}%");
                            })
                            ->orWhere('booking_number', 'like', "%{$search}%");
                    });
                }
            }

            if ($request->filled('status') && $request->status !== 'all') {
                $query->where('status', $request->status);
            }

            $refunds = $query->latest()->get();

            $filename = 'refund-requests-' . now()->format('Y-m-d-H-i-s');
            $filename .= '.xlsx';

            return \Excel::download(new \App\Exports\RefundRequestsExport($refunds), $filename);
        } catch (\Exception $e) {
            Log::error('Refund export error: ' . $e->getMessage());
            return redirect()->back()->with([
                'title' => 'Error',
                'message' => 'Failed to export refund requests. Please try again.',
                'type' => 'error'
            ]);
        }
    }
    public function adminWallet()
    {
        // Get customer transactions (individual booking entries)
        $customerTransactions = Booking::with(['service', 'service.category', 'customer', 'provider'])
            ->whereHas('customer')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get professional transactions (user subscriptions)
        $professionalTransactions = UserSubscription::with(['user', 'subscription'])
            ->orderBy('start_date', 'desc')
            ->get();

        // Calculate wallet statistics
        $paidByCustomers = $customerTransactions->whereIn('status', [0, 1, 2, 4])->sum(function ($booking) {
            return $booking->service->price ?? 0;
        });
        $paidByProfessionals = $professionalTransactions->where('status', 1)->sum('subscription_price');
        $totalRevenue = $paidByCustomers + $paidByProfessionals;
        $numberOfBookings = $customerTransactions->count();
        $numberOfSubscriptions = $professionalTransactions->count();

        // Get categories for filter dropdown
        $categories = \App\Models\Category::active()->get();

        $walletStats = [
            'paid_by_customers' => $paidByCustomers,
            'paid_by_professionals' => $paidByProfessionals,
            'total_revenue' => $totalRevenue,
            'number_of_bookings' => $numberOfBookings,
            'number_of_subscriptions' => $numberOfSubscriptions,
        ];

        return view('dashboard.admin.wallet', compact('customerTransactions', 'professionalTransactions', 'categories', 'walletStats'));
    }

    public function filterWalletCustomers(Request $request)
    {
        $query = Booking::with(['service', 'service.category', 'customer', 'provider'])
            ->whereHas('customer')
            ->orderBy('created_at', 'desc');

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('booking_number', 'like', "%{$search}%")
                    ->orWhereHas('customer', function ($customerQuery) use ($search) {
                        $customerQuery->where('name', 'like', "%{$search}%")
                            ->orWhere('email', 'like', "%{$search}%");
                    })
                    ->orWhereHas('service', function ($serviceQuery) use ($search) {
                        $serviceQuery->where('name', 'like', "%{$search}%");
                    });
            });
        }

        // Apply status filter (same logic as booking page)
        if ($request->filled('status') && $request->get('status') !== 'all') {
            $status = $request->get('status');
            switch ($status) {
                case 'ongoing':
                    $query->where('status', 0)->whereRaw("CONCAT(booking_date, ' ', booking_time) < ?", [now()]);
                    break;
                case 'upcoming':
                    $query->where('status', 0)->whereRaw("CONCAT(booking_date, ' ', booking_time) >= ?", [now()]);
                    break;
                case 'complete':
                    $query->where('status', 1);
                    break;
                case 'cancelled':
                    $query->where('status', 2);
                    break;
                case 'refunded':
                    $query->where('status', 3);
                    break;
            }
        }

        // Apply category filter
        if ($request->filled('category') && $request->get('category') !== 'all') {
            $category = $request->get('category');
            $query->whereHas('service', function ($serviceQuery) use ($category) {
                $serviceQuery->whereHas('category', function ($catQuery) use ($category) {
                    $catQuery->where('name', $category);
                });
            });
        }

        // Apply date filter
        if ($request->filled('date')) {
            $dateRange = $request->get('date');
            if (strpos($dateRange, ' - ') !== false) {
                [$startDate, $endDate] = explode(' - ', $dateRange);
                $startDate = \Carbon\Carbon::parse($startDate)->startOfDay();
                $endDate = \Carbon\Carbon::parse($endDate)->endOfDay();
                $query->whereBetween('created_at', [$startDate, $endDate]);
            }
        }

        $transactions = $query->get();

        $html = view('dashboard.admin.partials.wallet-customers-table', compact('transactions'))->render();

        return response()->json([
            'success' => true,
            'html' => $html,
            'count' => $transactions->count()
        ]);
    }

    public function filterWalletProfessionals(Request $request)
    {
        \Log::info('filterWalletProfessionals method called', ['request' => $request->all()]);

        $query = UserSubscription::with(['user', 'subscription'])
            ->orderBy('start_date', 'desc');

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->whereHas('subscription', function ($subscriptionQuery) use ($search) {
                    $subscriptionQuery->where('name', 'like', "%{$search}%");
                })
                    ->orWhereHas('user', function ($userQuery) use ($search) {
                        $userQuery->where('name', 'like', "%{$search}%")
                            ->orWhere('email', 'like', "%{$search}%");
                    });
            });
        }

        // Apply status filter (Active/Inactive for subscriptions)
        if ($request->filled('status') && $request->get('status') !== 'all') {
            $status = $request->get('status');
            switch ($status) {
                case 'active':
                    $query->where('status', 1);
                    break;
                case 'inactive':
                    $query->where('status', 0);
                    break;
            }
        }

        // Apply date filter - filter by subscription start_date and end_date
        if ($request->filled('date')) {
            $dateRange = $request->get('date');
            \Log::info('Professional date filter received:', ['date' => $dateRange]);

            if (strpos($dateRange, ' - ') !== false) {
                [$filterStartDate, $filterEndDate] = explode(' - ', $dateRange);

                try {
                    // Try multiple date formats
                    $startDate = null;
                    $endDate = null;

                    // Try MMM DD, YYYY format first
                    try {
                        $startDate = \Carbon\Carbon::createFromFormat('MMM DD, YYYY', trim($filterStartDate));
                        $endDate = \Carbon\Carbon::createFromFormat('MMM DD, YYYY', trim($filterEndDate));
                    } catch (\Exception $e) {
                        // Try MMM D, YYYY format (single digit day)
                        try {
                            $startDate = \Carbon\Carbon::createFromFormat('MMM D, YYYY', trim($filterStartDate));
                            $endDate = \Carbon\Carbon::createFromFormat('MMM D, YYYY', trim($filterEndDate));
                        } catch (\Exception $e2) {
                            // Fallback to Carbon parse
                            $startDate = \Carbon\Carbon::parse(trim($filterStartDate));
                            $endDate = \Carbon\Carbon::parse(trim($filterEndDate));
                        }
                    }

                    if ($startDate && $endDate) {
                        $startDate = $startDate->startOfDay();
                        $endDate = $endDate->endOfDay();

                        \Log::info('Professional date filter parsed:', [
                            'start' => $startDate->format('Y-m-d H:i:s'),
                            'end' => $endDate->format('Y-m-d H:i:s')
                        ]);

                        // Filter subscriptions where the subscription period overlaps with the selected date range
                        $query->where(function ($q) use ($startDate, $endDate) {
                            $q->where(function ($subQuery) use ($startDate, $endDate) {
                                // Subscription starts within the filter range
                                $subQuery->whereBetween('start_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')]);
                            })->orWhere(function ($subQuery) use ($startDate, $endDate) {
                                // Subscription ends within the filter range
                                $subQuery->whereBetween('end_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')]);
                            })->orWhere(function ($subQuery) use ($startDate, $endDate) {
                                // Subscription spans the entire filter range
                                $subQuery->where('start_date', '<=', $startDate->format('Y-m-d'))
                                    ->where('end_date', '>=', $endDate->format('Y-m-d'));
                            });
                        });
                    }
                } catch (\Exception $e) {
                    \Log::error('Professional date filter parsing error:', ['error' => $e->getMessage(), 'date' => $dateRange]);
                }
            }
        }

        $transactions = $query->get();

        \Log::info('Professional transactions query result:', [
            'count' => $transactions->count(),
            'sql' => $query->toSql(),
            'bindings' => $query->getBindings()
        ]);

        $html = view('dashboard.admin.partials.wallet-professionals-table', compact('transactions'))->render();

        return response()->json([
            'success' => true,
            'html' => $html,
            'count' => $transactions->count(),
            'debug' => [
                'date_filter' => $request->get('date'),
                'sql' => $query->toSql(),
                'bindings' => $query->getBindings()
            ]
        ]);
    }

    public function exportWalletCustomers(Request $request)
    {
        $query = Booking::with(['service', 'service.category', 'customer', 'provider'])
            ->whereHas('customer')
            ->orderBy('created_at', 'desc');

        // Apply same filters as filterWalletCustomers
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('booking_number', 'like', "%{$search}%")
                    ->orWhereHas('customer', function ($customerQuery) use ($search) {
                        $customerQuery->where('name', 'like', "%{$search}%")
                            ->orWhere('email', 'like', "%{$search}%");
                    })
                    ->orWhereHas('service', function ($serviceQuery) use ($search) {
                        $serviceQuery->where('name', 'like', "%{$search}%");
                    });
            });
        }

        if ($request->filled('status') && $request->get('status') !== 'all') {
            $status = $request->get('status');
            switch ($status) {
                case 'ongoing':
                    $query->where('status', 0)->whereRaw("CONCAT(booking_date, ' ', booking_time) < ?", [now()]);
                    break;
                case 'upcoming':
                    $query->where('status', 0)->whereRaw("CONCAT(booking_date, ' ', booking_time) >= ?", [now()]);
                    break;
                case 'complete':
                    $query->where('status', 1);
                    break;
                case 'cancelled':
                    $query->where('status', 2);
                    break;
                case 'refunded':
                    $query->where('status', 3);
                    break;
            }
        }

        if ($request->filled('category') && $request->get('category') !== 'all') {
            $category = $request->get('category');
            $query->whereHas('service', function ($serviceQuery) use ($category) {
                $serviceQuery->whereHas('category', function ($catQuery) use ($category) {
                    $catQuery->where('name', $category);
                });
            });
        }

        if ($request->filled('date')) {
            $dateRange = $request->get('date');
            if (strpos($dateRange, ' - ') !== false) {
                [$startDate, $endDate] = explode(' - ', $dateRange);
                $startDate = \Carbon\Carbon::parse($startDate)->startOfDay();
                $endDate = \Carbon\Carbon::parse($endDate)->endOfDay();
                $query->whereBetween('created_at', [$startDate, $endDate]);
            }
        }

        $transactions = $query->get();

        return \Excel::download(new \App\Exports\WalletCustomersExport($transactions), 'wallet-customers-' . now()->format('Y-m-d') . '.xlsx');
    }

    public function exportWalletProfessionals(Request $request)
    {
        $query = UserSubscription::with(['user', 'subscription'])
            ->orderBy('created_at', 'desc');

        // Apply same filters as filterWalletProfessionals
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->whereHas('subscription', function ($subscriptionQuery) use ($search) {
                    $subscriptionQuery->where('name', 'like', "%{$search}%");
                })
                    ->orWhereHas('user', function ($userQuery) use ($search) {
                        $userQuery->where('name', 'like', "%{$search}%")
                            ->orWhere('email', 'like', "%{$search}%");
                    });
            });
        }

        if ($request->filled('status') && $request->get('status') !== 'all') {
            $status = $request->get('status');
            switch ($status) {
                case 'active':
                    $query->where('status', 1);
                    break;
                case 'inactive':
                    $query->where('status', 0);
                    break;
            }
        }

        if ($request->filled('date')) {
            $dateRange = $request->get('date');
            if (strpos($dateRange, ' - ') !== false) {
                [$startDate, $endDate] = explode(' - ', $dateRange);
                $startDate = \Carbon\Carbon::parse($startDate)->startOfDay();
                $endDate = \Carbon\Carbon::parse($endDate)->endOfDay();
                $query->whereBetween('created_at', [$startDate, $endDate]);
            }
        }

        $transactions = $query->get();

        return \Excel::download(new \App\Exports\WalletProfessionalsExport($transactions), 'wallet-professionals-' . now()->format('Y-m-d') . '.xlsx');
    }

    public function subscriptionDetail(Request $request, $id)
    {
        $subscription = UserSubscription::with(['user', 'subscription'])->findOrFail($id);

        return view('dashboard.admin.subscription-detail', compact('subscription'));
    }

    public function adminVat()
    {
        return view('dashboard.admin.vat-mgmt');
    }

    /**
     * Apply coupon to cart and calculate discount
     */
    public function applyCoupon(Request $request)
    {
        try {
            $couponCode = $request->input('coupon_code');
            if (!$couponCode) {
                return response()->json(['status' => false, 'message' => 'Please enter a coupon code']);
            }

            // Get coupon from database (service coupons only)
            $coupon = DiscountCoupon::where('coupon_code', $couponCode)
                ->where('status', 1)
                ->where('discount_type', 'service')
                ->where('start_date', '<=', now())
                ->where('end_date', '>=', now())
                ->where('user_limit', '>', 0)
                ->first();

            if (!$coupon) {
                return response()->json(['status' => false, 'message' => 'Invalid or expired coupon code']);
            }

            if ($coupon->user_limit && $coupon->used_count >= $coupon->user_limit) {
                return response()->json(['status' => false, 'message' => 'This coupon has reached its usage limit']);
            }
            $cartItems = Session::get('booking_cards', []);
            if (empty($cartItems)) {
                return response()->json(['status' => false, 'message' => 'Your cart is empty']);
            }
            $originalTotal = 0;
            $excludedServices = [];
            if ($coupon->applies_to === 'service') {
                $validServices = $coupon->services->pluck('pivot.service_id')->toArray();
                $invalidServices = [];
                foreach ($cartItems as $item) {
                    if (!in_array($item['service']['id'], $validServices)) {
                        $invalidServices[] = $item['service']['name'];
                    }
                }
                if (!empty($invalidServices)) {
                    $serviceNames = implode(', ', $invalidServices);
                    return response()->json([
                        'status' => false,
                        'message' => "This coupon cannot be applied to: {$serviceNames}. Please remove these services to apply the coupon."
                    ]);
                }
                foreach ($cartItems as $item) {
                    $originalTotal += $item['service']['price'] ?? 0;
                }
            } elseif ($coupon->applies_to === 'category') {
                // Get valid category IDs from the coupon_category pivot table
                $validCategoryIds = $coupon->categories->pluck('id')->toArray();

                if (empty($validCategoryIds)) {
                    return response()->json([
                        'status' => false,
                        'message' => "This coupon has no valid categories configured."
                    ]);
                }

                $validServices = [];
                $invalidServices = [];

                foreach ($cartItems as $item) {
                    if (in_array($item['service']['category_id'], $validCategoryIds)) {
                        $originalTotal += $item['service']['price'] ?? 0;
                        $validServices[] = $item['service']['name'];
                    } else {
                        $invalidServices[] = $item['service']['name'];
                    }
                }

                // If no services match any of the valid categories, reject the coupon
                if (empty($validServices)) {
                    return response()->json([
                        'status' => false,
                        'message' => "This coupon is only valid in specified category. None of your cart items qualify."
                    ]);
                }

                // Allow partial application - coupon will only apply to matching category services
            } else {
                foreach ($cartItems as $item) {
                    $originalTotal += $item['service']['price'] ?? 0;
                }
            }

            // Calculate discount based on type
            $discountAmount = 0;
            $discountMessage = '';

            if ($coupon->type === 'percentage') {
                $discountAmount = ($originalTotal * $coupon->discount) / 100;
                $discountMessage = "Coupon applied! You saved {$coupon->discount}%";
            } elseif ($coupon->type === 'amount') {
                $discountAmount = $coupon->discount;
                $discountMessage = "Coupon applied! You saved $" . number_format($coupon->discount, 2);
            } else {
                // Fallback for old coupons without type (assume percentage)
                $discountAmount = ($originalTotal * $coupon->discount) / 100;
                $discountMessage = "Coupon applied! You saved {$coupon->discount}%";
            }

            // Calculate new total
            $newTotal = $originalTotal - $discountAmount;

            // Validation: If total becomes less than 0, don't apply coupon
            if ($newTotal < 0) {
                return response()->json([
                    'status' => false,
                    'message' => 'Coupon discount amount exceeds cart total. Cannot apply this coupon.'
                ]);
            }

            // Store coupon in session
            Session::put('applied_coupon', [
                'code' => $couponCode,
                'type' => $coupon->type,
                'discount_value' => $coupon->discount,
                'discount_amount' => $discountAmount,
                'original_total' => $originalTotal,
                'new_total' => $newTotal
            ]);

            return response()->json([
                'status' => true,
                'message' => $discountMessage,
                'discount_amount' => $discountAmount,
                'original_total' => $originalTotal,
                'new_total' => $newTotal
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'An error occurred: ' . $e->getMessage()
            ]);
        }
    }
    /**
     * Get current cart total from session
     */
    public function getCartTotal()
    {
        try {
            $cartItems = Session::get('cart', []);
            $total = 0;

            foreach ($cartItems as $item) {
                $total += $item['service']['price'] ?? 0;
            }

            // Add VAT
            $total += 17.84; // This should come from settings

            return response()->json([
                'status' => true,
                'total' => $total
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to calculate cart total'
            ]);
        }
    }
}
