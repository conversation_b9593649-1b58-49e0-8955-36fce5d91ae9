/* Sora */
@import url('https://fonts.googleapis.com/css2?family=Hanken+Grotesk:ital,wght@0,100..900;1,100..900&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Manrope:wght@200..800&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Raleway:ital,wght@0,100..900;1,100..900&family=Rubik:ital,wght@0,300..900;1,300..900&family=Sora:wght@100..800&display=swap');
/* Inter */
@import url('https://fonts.googleapis.com/css2?family=Hanken+Grotesk:ital,wght@0,100..900;1,100..900&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Manrope:wght@200..800&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Raleway:ital,wght@0,100..900;1,100..900&family=Rubik:ital,wght@0,300..900;1,300..900&family=Sora:wght@100..800&display=swap');
/* Nunito Sans */
@import url('https://fonts.googleapis.com/css2?family=Hanken+Grotesk:ital,wght@0,100..900;1,100..900&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Manrope:wght@200..800&family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Raleway:ital,wght@0,100..900;1,100..900&family=Rubik:ital,wght@0,300..900;1,300..900&family=Sora:wght@100..800&display=swap');
/*  Outfit */
@import url('https://fonts.googleapis.com/css2?family=Hanken+Grotesk:ital,wght@0,100..900;1,100..900&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Manrope:wght@200..800&family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Outfit:wght@100..900&family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Raleway:ital,wght@0,100..900;1,100..900&family=Rubik:ital,wght@0,300..900;1,300..900&family=Sora:wght@100..800&display=swap');
* { margin: 0; padding: 0; box-sizing: border-box;}
html{ scroll-behavior: smooth;}

:root {
    --white: #fff;
    --black: #000;
    --neutral-gray:#F0F0F0;
    --whisper-gray:#F7F7F7 ;
    --deep-blue:#020C87;
    --semi-transparent-black:rgba(0, 0, 0, 0.5);
    --gray:#A5A5A5;
    --ocean-blue:#065FAA;
    --steel-blue:#4682B4;
    --slate-gray:#708090;
    --snow-white:#FAFAFA;
    --gray:rgba(255, 255, 255, 0.30);;
    --light-gray:#6E7A84;
    --dark-blue:#1E3A5F;
    --light-black:#363636;
    --ice-blue:#F4F9FF;
    --cultured-white:#F9F9F9;
    --red:#EA4335;
    --dull-gray:rgba(54, 54, 54, 0.60);
    --link-gray:#9A9EA6;
    --navy-blue:#172633;
    --border-color:#E5E7EB;
    --yellow:#FCAA00;
    --light-blue:#3B82F6;
    --green:#059669;
    --light-green:#0BC688;
    --dark-black:#111827;
    --purple:#884DFF;
    --orange:#F1962D;
    --bg-color:#F3F4F6;
    --input-border:#DCDDE8;
    --dark-cool-gray:#6B7280;
    --body-grey: #F3F4F6;
    --bg-light-green:#00E096;
    --bg-blue:#0095FF;
    --bg-orange:#FF8F0D;
    --bg-purple:#6f42c1;
    --card-text-blue:#0D121F;
    --orange-icon:#F28E1D;
    --light-azure:#55ADFF;
    --faint-blue:rgba(85, 173, 255, 0.08);
    --charcoal-grey:#4B5563;
    --light-orange:#F59E0B;
    --dark-green:#10B981;
    --dark-red:#EF4444;
}

.app-default, body { background: var(--body-grey);}
.app-default.customer_dashboard { background: var(--white);}

/* Scrollbar */
::-webkit-scrollbar {  width: 10px;  background-color: transparent;  scroll-behavior: smooth;}
::-webkit-scrollbar-track {  background-color: transparent;  }
::-webkit-scrollbar-thumb {  background-color: var(--deep-blue);  border-radius: 20px;  }
::-webkit-scrollbar-thumb:hover {  background-color: #555;  }
/* scroll top */
.scrolltop {background: var(--whisper-gray);border: 1px solid transparent;}
.scrolltop:hover {border: 1px solid var(--ocean-blue);background: var(--white);}
.scrolltop:hover i {color: var(--ocean-blue);}

/* Font Sizes */
h1, h2, h3, h4, h5, h6 { line-height: 1.2; word-break:break-word; }
p, a, span { line-height: 1.5;  word-break: break-word;}

li { list-style: none;}
textarea { resize: none;}

h1{font-size: 48px; font-weight: 800;}
h2{font-size: 40px; font-weight: 800;}
h3{font-size: 39px; font-weight: 600;}
h4{font-size: 34px; font-weight: 600;}
h5{font-size: 27px; font-weight: 700;}
h6{font-size: 26px; font-weight: 500;}
.fs-24{font-size: 24px;}
p, .fs-18 { font-size: 18px; font-weight: 400;}

/* Font Weight */
.bold, .w-700 { font-weight: 700;}
.semi_bold { font-weight: 600;}
.regular { font-weight: 500;}
.normal { font-weight: 400;}

/* Font Sizes */
.fs-32{font-size: 32px;}
.fs-24{font-size: 24px;}
.fs-22{font-size: 22px}
.fs-20{font-size: 20px;}
.fs-16{font-size: 16px;}
.fs-15{font-size: 15px;}
.fs-14{font-size: 14px;}
.fs-13{font-size: 13px;}
.fs-12{font-size: 12px;}
.fs-11{font-size: 11px;}
.fs-10px, .fs-10 {font-size: 10px;}

/* Font Family */
.inter{font-family: 'Inter', sans-serif}
.nunito-sans{font-family: 'Nunito Sans',  sans-serif;}
.sora{font-family: 'Sora',  sans-serif;}
.outfit{font-family: 'Outfit',  sans-serif;}

/* Buttons */
.button { color: var(--white); background-color: var(--deep-blue);border-radius: 30px;border: 1px solid transparent;text-align: center;font-family: 'Sora',  sans-serif;font-size: 14px;font-weight: 600; padding: 10px 38px;}
.button:hover , .blue-button:hover { background: transparent; color: var(--deep-blue);border: 1px solid var(--deep-blue);}
.button:hover i { color: var(--deep-blue);}
.button1 { background: transparent; color: var(--deep-blue) ;  border: 1px solid var(--deep-blue); font-family: Inter; font-size: 13px;font-weight: 600;border-radius: 20px; padding: 7px 37px;}
.blue-button { padding: 10px 62px; border: 1px solid var(--deep-blue); border-radius: 6px;color: var(--snow-white); background: var(--deep-blue);box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.10), 0 1px 2px -1px rgba(0, 0, 0, 0.10);font-family: 'Inter' , sans-serif;font-size: 13px;font-weight: 500;}
.trans-button { background: transparent; border: 1px solid var(--gray);}
.professional-btn { background-color: var(--ocean-blue);}
.professional-btn:hover { border: 1px solid var(--ocean-blue);color: var(--ocean-blue);}
.gray-btn { border-radius: 20px;padding: 7px 17px; cursor: pointer; display: block; width: max-content; /* font-family: Inter; font-size: 13px; */ border: 1px solid var( --border-color); background: var(--whisper-gray ); font-weight: 600;color: var(--black);}
.gray-btn.add-to-cart{float: inline-end;}

.add-btn{cursor: pointer;border:0;padding: 8px 18px; font-family: Inter;color: var(--white); font-size: 16px; font-style: normal; font-weight: 600; border-radius: 8px; background: var(--deep-blue);}
.add-btn i{color: var(--white);}
.purple-btn{cursor: pointer;padding: 12px 18px; font-family: Inter;color: var(--deep-blue); font-size: 12px; font-style: normal; font-weight: 500; border-radius: 8px; background: var(--bg-color);}
.cancel-btn { padding: 8px 14px;background: transparent; color: black;  font-size: 14px;  cursor: pointer;border-radius: 8px;border: 1px solid var(--neutral-gray);}
.save-btn{padding: 5px 16px; font-family: Inter;color: var(--white); font-size: 16px; font-style: normal; font-weight: 600; border-radius: 8px; background: var(--deep-blue);border: 1px solid transparent;}
.add-more-btn{   margin-top: 10px;   padding: 8px 14px;  background: transparent;  color: var(--deep-blue);  cursor: pointer;  border-radius: 8px; border: 1px solid var(--neutral-gray);text-align: center;font-family: Inter;font-size: 16px;font-weight: 600;}

/* Badge */
.white-badge{border-radius: 100px;border: 1px solid var(--neutral-gray);background: var(--white);color: var(--light-black); font-family: Inter;font-size: 13px;font-weight: 500;padding: 8px 12px;}
.light-blue-badge{width: fit-content; border-radius: 4px;background: var(--faint-blue);color: var(--light-azure); font-family: Sora;font-size: 12px;font-weight: 600;padding: 8px 12px;}
.blue-badge { border-radius: 20px;background: var(--deep-blue); padding: 3px 8px;}

/* Colors */
.white{color: var(--white);}
.black{color: var(--black);}
.light-gray{color:var(--light-gray);}
.steel-blue{color: var(--steel-blue);}
.dark-blue{color: var(--dark-blue);}
.light-black{color: var(--light-black);}
.dull-gray{color: var(--dull-gray);}
.link-gray{color: var(--light-gray);}
.deep-blue{color: var(--deep-blue);}
.light-azure{color: var( --light-azure);}
.green { color: var(--green);}
.red { color: var(--red);}
.dark-black { color:var(--dark-black);}
.dark-cool-gray { color: var(--dark-cool-gray)}
.card-text-blue {color: var(--card-text-blue);}

.bg_green { background-color: var(--green);}
.bg_red { background-color: var(--red);}
.bg-purple {background-color: var(--bg-purple);}
.bg-orange {background-color:var(--bg-orange);}
.bg-light-green {background-color:var(--bg-light-green);}
.bg-blue {background-color:var(--bg-blue);}
.bg-deep-blue{background-color: var(--deep-blue);}
.bg-light-blue{background-color: var(--light-blue);}
.bg-color{background-color: var(--bg-color);}

i.icon-color{color: var(--dark-blue);}

/* Spacing */
.padding-block{padding-bottom: 3em;}
.padding-inline{padding-inline: 6em;}
/* .padding{padding-block: 3em;} */

.opacity{opacity: 0.5;}
.opacity-6{opacity: 0.6;}
.opacity-8{opacity: 0.6;}

.border-right{border-right:1px solid var(--border-color);}
.letter-space{letter-spacing: 3px;}
input{border: none;background: transparent;}
input:focus{border: none; box-shadow: none; outline: 0}

/*Header*/
.header .header-search { border-radius: 30px; background: #EBF2F8;}
.discount-header { background: var(--navy-blue);}
.header-items { box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.06);}
.header-items .nav-item a.nav-link {color:var(--light-gray);position: relative;border-bottom: 1px solid transparent;transition: color 0.6s ease;}
/* .header-items .nav-item:last-child .nav-link {color: var(--deep-blue)} */
.header-items .nav-item a.dropdown-toggle, .Subscription-tabs li.nav-item .nav-link, .cms_tabs_wapper .nav-tabs .nav-link{color: var(--light-gray);}
.header-items .nav-item:has(.nav-link.active.header-active) a.dropdown-toggle{color: var(--white);}
.header-items .nav-item .nav-link.active.header-active, .Subscription-tabs li.nav-item .nav-link.active, .cms_tabs_wapper .nav-tabs .nav-link.active{ border-radius: 6px; background: var(--deep-blue);color: var(--white);    width: fit-content;}
.header-items .nav-item:has(.nav-link.active.header-active) { border-radius: 6px; background: var(--deep-blue);color: var(--white);}
.header-items .nav-item a.nav-link::before{content: '';position: absolute;left: 0;bottom: -1px;width: 100%;height: 1px;background-color: var(--deep-blue);transform: scaleX(0);transform-origin: left;transition: transform 0.6s ease;}
.header-items .nav-item  a.nav-link:hover::before{transform: scaleX(1);}
.header-items .nav-item a.nav-link:hover {color: var(--deep-blue);}
.header-items .nav-item .nav-link.active.header-active:hover{color: var(--white);}
.header-items .nav-item:has(.nav-link):hover a.dropdown-toggle{color: var(--deep-blue);}
.header-items .nav-item:has(.nav-link.active.header-active):hover  a.dropdown-toggle{color: var(--white);}

.navbar-header a.active-fav , .btn-custom.active-fav {border-radius: 30px;background: linear-gradient(0deg, rgba(255, 255, 255, 0.92) 0%, rgba(255, 255, 255, 0.92) 100%), var(--deep-blue);height: 40px;width: 40px;display: block;align-content: center;text-align: center;}
.navbar-header a.active-fav i ,.btn-custom.active-fav i{color: var(--deep-blue);}

section.dashboard-footer { background: #B8D1E7; padding: 20px; }

.frst-footer-sec , .end-footer-sec {display: flex; justify-content: space-between; align-items: center;}
.second-sec{background: #4B5BAD; padding: 20px; }
.end-footer-sec ul li{font-size: 15px; font-family: 'Sora',  sans-serif; font-weight: 400; text-decoration: underline; color: var(--white);}
.custom-select-location select, .custom-select-location select:focus-visible { border: none; outline: unset; font-size: 14px;}
.navbar-header i { font-size: 20px; color: #9A9EA6 !important;}
.app-navbar-item.user-info { border-radius: 20px; border: 1px solid var(--Light-Grey-2, #E5E7EB); background: #FFF; padding: 5px 8px; }
.app-navbar-item.user-info  i , .app-navbar-item.user-info span {color: var(--deep-blue); font-size: 12px;}
.navbar-header .symbol>img { width: 35px; height: 35px; border-radius: 50%; }
img.user-profile-pctr  {  border-radius: 50%; }

a.see-all-btn { border-radius: 6px; background:var(--whisper-gray); border: 1px solid var(--whisper-gray); padding: 10px; text-align: center; margin-top: 2em; color: var(--deep-blue); text-decoration: underline; font-weight: 700;}

/* Footer */
.footer{background: var(--whisper-gray);}
.footer .footer-border-bottom{border-bottom:1px solid var(--border-color);}
.footer .input-box{border-radius: 30px;padding: 9px 8px 9px 24px; background:var(--white);}
.footer .input-box input:focus{outline: 0; box-shadow: none;}
.footer .input-box input{border: none;}
.footer .input-box input[type="submit"]{border-radius: 30px;  background:var(--deep-blue); color: var(--white);    padding: 10px 26px;font-family: "Nunito Sans";font-size: 12px;font-weight: 700;}
.footer .input-box input::placeholder{ color :#9A9EA6;font-family: Inter;font-size: 14px;font-weight: 500;}

/*sidebar setting*/
/* .left-unset{left: 0 !important;margin-left: unset !important;}
.margin-left-unset{margin-left: unset !important;}
body .left-unset .app-container{padding: 0 !important;} */

.family-tabs .service-tab{ padding: 7px 12px; border-radius: 20px;width: fit-content; background: var(--cultured-white); color: #363636;border: 1px solid transparent; opacity: 0.6;font-family: Sora;font-size: 14px;font-weight: 400;}
.family-tabs .service-tab.active{ border: 1px solid var(--deep-blue); background: var(--white); color: var(--deep-blue); opacity:unset;font-weight: 600;}
.friends-cards{ border-radius: 6.76px; border: 1.127px solid var(--neutral-gray);  box-shadow: 0 1.127px 2.253px 0 rgba(0, 0, 0, 0.05);}
.blue-box { border-radius: 8px;background: var(--ice-blue);padding: 30px;}
.friends-cards .logo-box { border-radius: 6px; border: 1px solid var(--border-color); background: var(--white); padding: 8px 13px;}

/* Input Forms */
.form-control.form-input{ border-radius: 10px; border: 1px solid var(--input-border);padding: 14px 16px; background: var(--white); appearance: auto;}
.form-check-input:checked[type=radio] { appearance: initial; background-color: var(--deep-blue);}
.form-check-input {  width: 1rem;  height: 1rem;}
.form-check-input[type=radio] { border-radius: 50%;}
.form-check-input[type=radio] { border-color: var(--deep-blue);}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}


.custom-weeks-input input, .edit-certificationsmodal input, .edit-certificationsmodal textarea { border-radius: 10px; border: 1px solid var(--input-border);padding: 10px 16px; background: var(--white); appearance: auto;}
input[type=checkbox] { accent-color: #020C87;}
textarea:focus-visible{outline: none;}

.upload-box {height:150px;width:191px;padding: 14px;background: var(--white);cursor: pointer;border-radius: 4px;border: 2px dashed #DCDDE8;background: var(--white);display: flex;justify-content: center;align-items: center;flex-direction: column;gap: 10px;}
.preview-box img, .preview-box iframe {width: 100%;height: 100%;object-fit: contain;border-radius: 8px;}
.remove-file{color: var(--black);font-size: 16px;position: absolute;border: none;background: transparent;right: -22px;top: 35px;}
.remove-image {  position: absolute;  bottom:-7px; left:175px; color: var(--deep-blue); cursor: pointer; width: 25px; height: 25px; font-size: 12px; border-radius: 100px; border: 1px solid var(--deep-blue);  background: var(--white);}

.preview-box-file {position: relative;text-align: center;padding: 21px;width: 399px;border: 1px solid var(--Cool-Gray);border-radius: 10px;height: 93px;display: flex;align-items: center;gap: 30px;}
.preview-box-file i {font-size: 50px;}
.add-file i{color: var(--white); font-size: 20px;}
.add-file{border-radius: 4px;background: var(--charcoal-black);height: 22px;width: 22px;display: flex;align-items: center;justify-content: center;padding: 15px;}
.remove-file { border-radius: 50%;height: 21px;width: 20px;background: white;border: none;}
.preview-box-file {display: flex; justify-content: center; align-items: center; gap:20px;}
.preview-box-file i.fa-file-pdf{font-size: 40px;}
.upload-box:has(.preview-box-file) .add-file , .upload-box:has(.preview-box-file) .upload-text {display: none;}
.upload-box img{height: 100%; width: 100%;object-fit: contain;}
.white-box{ border-radius: 8px; border: 1.127px solid var(--neutral-gray); background: #FFF; box-shadow: 0px 1.127px 2.253px 0px rgba(0, 0, 0, 0.05);}

/*business analytics*/
.card-box{border-radius: 8px;padding: 14px;border-bottom: 1px solid var(--border-color);background: var(--white);box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10);height: 100%;}
.icon_cards {padding: 14px;border-radius: 6px;}
.card-box:has(.bg-light-blue) .icon_cards{background-color:  var(--light-blue);}
.card-box:has(.bg-light-green) .icon_cards{background-color: var(--light-green);}
.card-box:has(.bg-purple) .icon_cards{background-color: var(--purple);}
.card-box:has(.bg-orange) .icon_cards{background-color: var(--orange);}
.card-box:has(.bg-deep-blue)  .icon_cards{background-color: var(--deep-blue);}
.card-wrapper .analytics-green-arrow{color:var(--green) ;}
.card-wrapper .analytics-red-arrow{color:var(--red) ;}

/* .card-wrapper > div:nth-child(1) .card-box .icon_cards {background-color: var(--light-blue);}
.card-wrapper > div:nth-child(2) .card-box .icon_cards {background-color: var(--light-green);}
.card-wrapper > div:nth-child(3) .card-box .icon_cards {background-color: var(--purple);}
.card-wrapper > div:nth-child(4) .card-box .icon_cards {background-color: var(--orange);} */

.red-box { border-radius: 9999px; background: rgba(234, 67, 53, 0.10); padding: 4px; text-align: center;}
.green-box{border-radius: 9999px;background: rgba(5, 150, 105, 0.10);padding: 4px; text-align: center;}
.select-wrapper {position: relative;display: inline-block;}
.select-wrapper select {appearance: none;-webkit-appearance: none;-moz-appearance: none;padding: 10px 40px 10px 16px;font-size: 16px;font-weight: 600;color: var(--light-black);opacity: 0.6;cursor: pointer;box-shadow: 0 1px 3px rgba(0,0,0,0.1);outline: none;width: 150px;border-radius: 8px;border: 1px solid var(--border-color);background: var(--white);}
.select-wrapper::after {content: '\f078';font-family: "Font Awesome 5 Free";font-weight: 900;font-size: 12px;color: var(--light-black);position: absolute;top: 22px;right: 16px;transform: translateY(-50%);pointer-events: none;}
select option {padding: 10px;}
.drop-btn { border-radius: 6px; background: var(--body-grey); border: none; padding: 6px 10px;}
.dropdown-menu { border-radius: 6px; border: 0.5px solid var(--border-color); box-shadow: 0 10px 15px -3px var(--body-grey), 0 4px 6px -4px var(--body-grey); padding: 0; overflow: hidden;}
.dropdown-menu li:not(:last-child) { border-bottom: 1px solid var(--border-color); }
.dropdown-item {display: flex;align-items: center;gap: 8px;font-weight: 500;font-size: 14px;}
.dropdown-item i {font-size: 14px;}
.dropdown-item.complete , .dropdown-item.complete .complete-icon {color: var(--green);}
.dropdown-item.cancel , .dropdown-item.cancel .cancel-icon {color: var(--red);}
/* .dropdown-item.cancel , .dropdown-item.cancel .view-icon {color: var(--steel-blue);} */

/* business services */
.add-friend .form-control.form-inputs-field , .add-friend .form-select.form-select-field{padding: 14px 12px 11px 16px;border-radius: 10px;border: 1px solid var(--input-border);}

.holiday-form .form-control.form-inputs-field, body .holiday-form .select2-container .select2-selection--single{height: 48px;}

.form-control.form-inputs-field , .form-select.form-select-field{padding: 11px 12px 11px 16px;border-radius: 10px;border: 1px solid var(--input-border);}

.form-control.form-inputs-field::placeholder , .form-select.form-select-field::placeholder{color: var(--black);opacity: 0.3;font-family: Inter;font-size: 14px;font-weight: 400;}
.form-label.form-input-labels{ color:var(--black);font-family: Inter;font-size: 14px;font-weight: 500;}
.form-add-services .image-label, .form-add-category .image-input-empty{display: flex;top: 0;left: 0;margin: auto; bottom: 0;right: 0;transform: unset;border-radius: 10px;}
.form-add-services .image-input, .form-add-category .image-input-empty {border-radius: 10px;border: 1px dashed var(--input-border);background: var(--white);padding: 14px;}
.form-add-services .image-input:not(.image-input-empty) label.image-label {display: none;}
.form-add-category .image-input-empty[type="checkbox"] { appearance: none;width: 16px; height: 16px;border: 1px solid var(--input-border);background-color: var(--white);cursor: pointer;border-radius: 4px;}
.form-add-services input[type="checkbox"]:checked {background-color: var(--deep-blue);border: 1px solid transparent;}
.form-add-services input[type="checkbox"]:checked::after {content: "\f00c";display: block;text-align: center;color: var(--white);font-size: 9px;font-family: 'Font Awesome 6 Free';font-weight: 600;border: 1px solid transparent;}
.calender-button{position: absolute;right: 10px;top: 10px;bottom: auto;background: transparent;border: none;}
.form-add-services .input-box{ width: 100%; border: 1px solid var(--input-border);background-color: var(--white);cursor: pointer;border-radius: 4px; padding: 8px;}

/* tabs serives add */
.add-service .business-services{border-radius: 20px;background: var(--white);color:var(--light-black);font-family: Sora;font-size: 14px;font-weight: 400;border: 1px solid transparent;}
.add-service .business-services.active{font-weight: 600;border-radius: 20px;background: var(--white);border: 1px solid var(--deep-blue);color: var(--deep-blue);}
.add-service .business-services.active .icon-svg path { stroke: var(--deep-blue) !important;stroke-opacity: 1;}

/* setting-tabs */
.business-setting .setting-nav .setting-tabs{padding: 10px;color: var(--light-black);font-family: Sora;font-size: 16px;font-weight: 400;}
.business-setting .setting-nav .setting-tabs.active{border-radius: 4px;background:var(--body-grey);width: 100%;text-align: start;font-weight: 600; color: var(--black);}
.business-setting  .toggle-password {color: var(--black);opacity: 0.3;  position: absolute;cursor: pointer;top: 44px;right: 25px;bottom: auto;}
.business-setting li{list-style:unset;}

/*business home page*/
.booking_card {border: 1px solid #ddd;border-radius: 8px;background: #fff;padding:0 16px;margin-bottom: 12px;}
.booking_card .date-box {text-align: center;background: var(--whisper-gray);padding: 15px;margin: 5px 10px;border-radius: 5px;}
.booking_card .date-box .day {font-weight: bold;font-size: 18px;}
.booking_card .date-box .month {color: gray;font-size: 14px;}
.booking_card .details {display: flex;flex-direction: column;}
.booking_card .service-name {font-weight: bold;margin-bottom: 4px;}
.booking_card .service-time {font-size: 12px;color: gray;}
.booking_card .service-time .status {font-weight: 600;margin-left: 8px;text-transform: uppercase; padding: 4px; font-size: 12px;}
.booking_card .status.complete {color: green;border-radius: 4px;background: rgba(16, 185, 129, 0.10);}
.booking_card .status.booked {color: blue;border-radius: 4px;background: rgba(6, 95, 170, 0.10);}
.booking_card .status.cancelled {color: red;border-radius: 4px;background: rgba(234, 67, 53, 0.10);}

.select-wrapper .weekly-dropdown{background: var(--whisper-gray);border-radius: 38px;border: 1px solid transparent;color: var(--light-black);padding: 8px 40px 8px 16px;}
.notification-bg {border-radius: 10px;background: white;padding: 10px;}

.business-home .card-box .scrollbar::-webkit-scrollbar-thumb {background-color: #C2A8F4;border: 4px solid transparent;border-radius: 8px;background-clip: padding-box;}
.business-home .card-box .scrollbar::-webkit-scrollbar {width: 16px;}
.business-home .card-box .scrollbar {overflow-y: auto;overflow-x: hidden;max-height: 546px;border-radius: 8px;}
.noti-content {scrollbar-width: unset;scrollbar-color: unset;border:none; border-radius:0;border-bottom: 1px solid var(--body-grey);}
.noti-content:hover {scrollbar-color: unset;}

.book-time-card {border-radius: 5px;border: none;padding: 20px;border: 1px solid var(--border-color);box-shadow: none;overflow-x: hidden;width: 100%;}
.progress {height: 8px;background-color: #f1f1f1;border-radius: 4px;}
.progress-bar {border-radius: 4px;}
.line-clamp-1 {display: -webkit-box;-webkit-line-clamp: 1;-webkit-box-orient: vertical;overflow: hidden;text-overflow: ellipsis;}
.view-all-btn { padding-bottom: 2px;border-bottom: 1px solid var(--deep-blue);transition: all 0.3s ease;color: var(--deep-blue);}
.view-all-btn .arrow {margin-left: 6px;transition: transform 0.3s ease;}
.view-all-btn:hover {color: #0070cc;border-bottom-color: #0070cc;}
.view-all-btn:hover .arrow {transform: translateX(4px); color: #0070cc;}

/* Custom Date range */
.daterangepicker .drp-calendar td.active.start-date, .daterangepicker .drp-calendar td.active.end-date, .daterangepicker .ranges li.active, .daterangepicker .drp-buttons .applyBtn  { background-color: var( --deep-blue) !important; color: #fff !important;}
.daterangepicker .drp-calendar td.in-range.available:not(.active):not(.off):not(.today) { background-color: #006AA020; color: #000;}
.daterangepicker .drp-buttons .cancelBtn { background-color: transparent; border: 1px solid #0000001A !important; color: var( --deep-blue); }
.daterangepicker .ranges li:hover, .daterangepicker .drp-calendar td.available:hover, .daterangepicker .drp-calendar th.available:hover { color: var( --deep-blue); }
.daterangepicker .drp-calendar td.in-range.available:not(.active):not(.off):not(.today):hover { background-color: var( --deep-blue); color: #fff !important; border-top-left-radius: 0; border-bottom-left-radius: 0;}

/* Custom Date range End */

/* Data Table CSS */
.table-container { background: transparent; border-radius: 12px; overflow: visible;  padding: 5px; }
#responsiveTable { width: 100% !important; border-collapse: separate; border-spacing: 0 10px; border: none; }
#responsiveTable thead { display: none; }
#responsiveTable td { padding: 19px; vertical-align: middle; background: var(--white); position: relative; border: 1px solid var(--neutral-gray); border-right: none; border-left: none; box-shadow: none; font-size: 15px; font-weight: 400; color: #000; }

/* Add column header before each cell */
#responsiveTable td::before { content: attr(data-label); display: block; font-weight: 500; color: #9a9ea6; margin-bottom: 5px; font-size: 12px; text-transform: uppercase;}
#responsiveTable tr { transition: all 0.3s ease;  box-shadow: 0px 1px 3px 0px #0000001A; border-radius: 8px; }
#responsiveTable tr td:first-child {  border-top-left-radius: 8px;  border-bottom-left-radius: 8px; border-right: none;  border-left: 1px solid var(--neutral-gray);}
#responsiveTable tr td:last-child {  border-top-right-radius: 8px; border-bottom-right-radius: 8px;  border-left: none; border-right: 1px solid var(--neutral-gray);}
#responsiveTable td  .card{border: 0;border-right: 1px solid var(--border-color);border-radius: 0;}
#responsiveTable .card-header img { height: 80px; width: 80px; border-radius: 10px;}

/* Pagination styles */
body .dataTables_wrapper .dataTables_paginate .paginate_button { padding: 6px 12px;  margin: 0 3px; border-radius: 6px; border: 1px solid #ddd; transition: all 0.2s; background-color: #fff; }
body .dataTables_wrapper .dataTables_paginate .paginate_button:hover {   background: #eee; border-color: #ccc;  color: var(--deep-blue) !important; }
body .dataTables_wrapper .dataTables_paginate .paginate_button.current { background: var(--deep-blue); color: white !important; border-color: var(--deep-blue); }
body .dataTables_wrapper .dataTables_paginate .paginate_button.disabled { opacity: 0.3;}
body .dataTables_wrapper .dataTables_paginate .paginate_button.disabled { opacity: 0.3; }
.table-container .dropdown-menu .dropdown-status .dot.all{background-color: var(--charcoal-grey);}
.table-container .dropdown-menu .dropdown-status .dot.ongoing{background-color: var(--light-orange);}
.table-container .dropdown-menu .dropdown-status .dot.upcoming{background-color: var(--light-blue);}
.table-container .dropdown-menu .dropdown-status .dot.completed{background-color: var(--dark-green);}
.table-container .dropdown-menu .dropdown-status .dot.cancelled-dot{background-color: var(--dark-red);}
/* Data Table End */

/* Filter Boxes */
.search_box {  position: relative; }
.search_box .search_input, .date_picker { border: 1px solid var(--border-color); min-width: 180px; outline: none; padding: 8px 10px; background-color: white; cursor: pointer; border-radius: 6px;}
.search_box .search { padding-left: 30px; }
.search_box label { position: absolute; top: 10px; left: 10px;}
.select-box select { appearance: none; -webkit-appearance: none; -moz-appearance: none; }
.select-box::after { content: '\f078'; font-family: "Font Awesome 5 Free"; font-weight: 900; font-size: 12px; color: var(--light-black); position: absolute; top: 20px; right: 16px;  transform: translateY(-50%);  pointer-events: none;}
.search_box.select-box .dropdown-toggle::after {  display: none; }
.dropdown-item .dot { width: 8px;  height: 8px;  border-radius: 50%;   display: inline-block; }
 
.staff-service.select-box::after {display: none;}
.staff-service.select-box{width: 250px;}

.staff-service .form-select.form-select-field { padding: 7px 12px 7px 16px; border-radius: 4px; border: 1px solid var(--input-border);}

/* Data Table CSS End */

/* notification */
.notification-sec .notification-header{border-bottom: 1px solid var(--border-color);}
/* .select2-container--bootstrap5 .select2-dropdown{width: max-content !important;} */
.notification-sec .scrollbar {max-height: 530px;}

/* subscription */
.subscription .pricing-card {box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);padding: 30px;background-color: #fff;position: relative;height: 100%;}
.subscription .pricing-card i{font-size: 24px;}
.subscription .pricing-card .card-body{height: 100%; padding: 10px 0;}
.pricing-card .current-plan-badge {position: absolute; top: 39px;right: 8px;background: #e0efff;font-size: 12px;font-weight: 600;padding: 4px 10px;border-radius: 999px;}
.pricing-card .features {list-style: none;padding: 1rem 0;margin: 20px 0;}
.pricing-card .features li {margin-bottom: 10px;display: flex;align-items: center;font-size: 18px;font-weight: 500;font-family: 'Inter';color:var(--light-black);opacity: 0.8;}
.pricing-card .features li::before {content: '\f00c';color: var(--white);margin-right: 10px;border-radius: 50px;background-color: var(--orange-icon);font-family: "Font Awesome 5 Free"; font-weight: 900; font-size: 12px;width: 20px;height: 20px;text-align: center;}
.pricing-card .action-btn {cursor:pointer;width: 100%;font-weight: 600;border-radius: 8px;padding: 10px 0;background-color: var(--deep-blue);border: 2px solid transparent;}
.pricing-card .action-btn:hover{background-color: transparent;border: 2px solid var(--deep-blue);color: var(--deep-blue);}
.pricing-card .subscribed-label {background: var(--deep-blue); width: 100%; color: var(--white); border: 1px solid #FFF; text-align: center; padding: 10px; border-radius: 8px; font-weight: 500;margin-top: 10px;}
.pricing-card .cancel-link {color: var(--red);text-align: center; margin-bottom: 10px;}

/* Style for the toggle container */
.toggle-container { display: flex;align-items: center;gap: 10px; width: 100px;}
.switch {position: relative; display: inline-block; width: 39px; height: 24px;}
.switch input { opacity: 0;width: 0;height: 0;}
.slider {position: absolute;cursor: pointer;top: 0;left: 0;right: 0;bottom: 0;background-color: #D8DAE5;border-radius: 34px;transition: 0.4s;}
.slider:before {content: ""; position: absolute;left: 4px;bottom: 4px; height: 16px;width: 16px; border-radius: 50%; background-color: var(--white);transition: 0.4s;}
input:checked + .slider {background-color: var(--dark-blue); }input:checked + .slider:before { transform: translateX(15px);}
.toggle-label { font-size: 12px; font-weight: 500;color:var(--black);}
input:checked ~ #toggleLabel {color: #4e73df; }

.header-icon li img { height: 20px; width: 20px; }
.notification-dropdown{border-radius: 8px;border: 0.5px solid var(--border-color);background:var(--white);box-shadow: 0px 0px 22px 0px rgba(0, 0, 0, 0.08);position: relative;z-index: 99999 !important;}

.booking-tabs  .calendar-view{border-radius: 20px; background:var(--white);color: #363636;font-family: Sora;font-size: 14px;font-weight: 400;border: 1px solid transparent;}
.booking-tabs  .calendar-view i{color: var(--light-black);}
.booking-tabs  .calendar-view.active{ background:var(--white);border: 1px solid var(--deep-blue);opacity: unset;color: var(--deep-blue);}
.booking-tabs  .calendar-view.active i{color: var(--deep-blue);}

.status span { position: relative;}
.status span::after { content: ''; position: absolute; left: -12px; top: 7px; height: 7px; width: 7px; border-radius: 50px;}
.wallet-time::after{top:7px; left:0}
/* .request-status::after{top: 51px;} */
.paid-status.status span::after {background-color: var(--dark-green);}
.unpaid-status.status span::after { background-color: var(--red);}
.ongoing-status.status span::after  {background-color:var(--light-blue);}
.reschedule-status.status span::after {background-color:var(--dark-cool-gray);}
.status-staff span::after { content: '';position: absolute; left: -11px; top: 8px; height: 7px; width: 7px;border-radius: 50px;}
.ongoing-status.status-staff span::after  {background-color:var(--light-blue);}
.reschedule-status.status-staff span::after {background-color:var(--dark-cool-gray);}


.reschedule-status span::after{background-color: var(--deep-blue);}
.pending-status span::after{background-color: var(--orange);}
.unpaid-status.status span::after { background-color: red;}
.pending-status.status span::after { background-color: var(--orange);}
.wallet-time.status span::after { background-color: var(--link-gray);}

/* #unregisteredTableBody .professional-status::after {  top: 50px;}
.admins-table .professional-status::after {top: 69px;} */

.top-rated-card { border-radius: 8px; border: 1px solid var(--neutral-gray); background: var(--cultured-white);}
.top-rated-card .card-header .top-rated-image {border-radius: 10px 10px 0 0;}
.top-rated-card .card-header .fav-icon {border-radius: 100px;border: 1px solid var(--neutral-gray);background: var(--white);height: 35px;width: 35px; padding: 8px; text-align: center; bottom: 7px;right: 7px; display: flex; justify-content: center; align-items: center;}

.heading-package i{font-size:24px;}
.input-group.features-all label {font-size: 14px;display: block;margin-bottom: 4px;font-weight: 500;color: #444;}
.input-group.features-all input {width: 100%;padding: 10px 12px;border-radius: 8px;border: 1px solid #ddd;font-size: 14px;}

/* image-input */
.settings-image-input  .image-input.image-input-outline .image-input-wrapper {border-radius: 50%;margin: -3px;}
.settings-image-input .image-input [data-kt-image-input-action=change] {left: 21em;top: 4em;width: 243px;z-index: 1;}
.image-input [data-kt-image-input-action=remove] {position: absolute;z-index: 1;left: 19em;top: 12em;border: 1px solid green;background: #F0F9F4; color: black;width: 123px; padding: 7px;display: flex;justify-content: center;}
.settings-image-input .image-input-empty, .form-add-services .image-input-empty  {background-image: url('/website/assets/images/image_input_holder.png'); background-size: cover; background-repeat: no-repeat;}
.form-add-services.sub-cateogry .image-input-empty {background-image: none;}

.cart-box .cart-image{border: 1px solid rgba(0, 0, 0, 0.08);background:var(--white);box-shadow: 0px 1px 2px -1px rgba(0, 0, 0, 0.10);}
.cart-box .card-body .blue-button{top: 35px; right: 10px;}
.cart-box .card-footer .blue-button{ font-size: 16px;  font-weight: 600;border-radius: 8px;}
.cart-box .card-body .discount-box{padding: 9px 13px;border-radius: 10px; border: 1px solid var(--input-border);background: var(--white);}
.cart-box .card-body .discount-box i{color: var(--red);font-size: 15px;}
.cart-section  .card-box .card-header .drop-btn{border: 1px solid var(--neutral-gray) !important;}
.drop-btn.delete-btn i{color: var(--red);font-size: 18px;}

.end-footer-sec .nav-link, .nav-link:hover a {  color: #FFF !important;}

 /* .drop-btn.delete-btn:hover{background-color: var(--deep-blue); border: 1px solid var(--deep-blue);color: var(--white);} */
.drop-btn.delete-btn:hover i{color: var(--deep-blue);}
.booking-confirm .booking-box{border-radius: 100px; border-bottom: 1px solid var(--border-color);padding: 38px 34px 36px 40px;background:var(--white); box-shadow: 0px 1px 2px -1px rgba(0, 0, 0, 0.10);}

/* header-swiper */
.swiper.header-swiper { width: 100%;}
.header-swiper .swiper-slide { width: fit-content;}
.header-swiper .swiper-button-next , .header-swiper .swiper-button-prev, #availabilityModal button#prevWeek, #availabilityModal button#nextWeek{ border-radius: 4px; background: var(--cultured-white); padding: 4px; height: 24px; width: 24px;}
.header-swiper .swiper-button-next:after, .header-swiper  .swiper-button-prev:after   {color: black;font-size: 14px;font-weight: 900;}
.header-swiper  .swiper-button-prev{right: 44px;left: auto;}
.header-swiper .swiper-button-prev , .header-swiper .swiper-button-next{top: 24px;}
.header-swiper {position: unset;}
.header-swiper .swiper-button-prev{ right: -32px;}
.header-swiper .swiper-button-next { right: -60px;}

/* date-picker */
.date_picker { position: relative; padding-left: 30px; padding-right: 30px; margin-bottom: 0;}
.date_picker .date-picker-container {  display: flex;align-items: center; width: 100%; }
.date_picker .date-picker-container .calender-icon {  left: 10px;   position: absolute;    font-size: 16px;    color: #6c757d; }
.date_picker  .date-picker-container .down-arrow { left: 200px; }

/* cart-stepper */
.step { display: none;transition: opacity 0.5s ease;}
.step.active {display: block;}
.back-arrow { cursor: pointer;font-size: 20px;z-index: 999999;position: relative;}
.step .previous-box{box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.06);position:absolute;; z-index:999;pointer-events: auto;right: 0;width: 100%;top: 0;overflow: hidden; background:var(--white); padding: 19px;}
.step .previous-box i{font-size: 20px; color:var(--black) ;}
.app-header.app-header-height { height: 0 !important;}
.app-header-height.margin-left-unset{margin-top: unset;}

/* analytics-charts */
.chart-container-booking {   position: relative;   width: 201px;height: 400px;}
#bookingChart { width: 100% !important;height: 100% !important;}
.card-details .add-btn {font-size: 13px;font-weight: 500;padding: 10px 16px;}
.card-details .trans-button {font-size: 13px;font-weight: 500;padding: 10px 16px;border-radius: 6px;border: 1px solid rgba(0, 0, 0, 0.10);  color: var(--black);}

/* select2 */
.select2-container--bootstrap5 .select2-dropdown .select2-results__option.select2-results__option--selected , .select2-container--bootstrap5 .select2-dropdown .select2-results__option.select2-results__option--highlighted{color:var(--deep-blue);}
.select2-container--bootstrap5 .select2-dropdown .select2-results__option.select2-results__option--selected:after{background-color: var(--deep-blue);}

/* Professional Profile Page */
.top-rated { border-radius: 20px; background: #FFF; font-size: 12px; padding: 5px 10px; }
     
/* border: 1px solid #FCAA00;  */
ul.sub-details li{list-style-type: disc; color: #363636;}
.feature-item .form-control:focus{border: 1px solid var(--bs-gray-300);}

.main-heading-icon img, .main-heading-icon i { border-radius: 8px; border-bottom: 1px solid #E5E7EB; background: #FFF; box-shadow: 0px 1px 2px -1px rgba(0, 0, 0, 0.10); padding: 8px; }
.main-heading-icon i { font-size: 24px; margin-left: 15px; }

.professional-swiper .swiper { width: 100%; height: 100%; }
.professional-swiper.swiper-slide { text-align: center; font-size: 18px; background: #fff; display: flex; justify-content: center; align-items: center; }
.professional-swiper .swiper-slide img { display: block; width: 100%; height: 100%; object-fit: cover; }

.certifications-logo .swiper-slide img{width: 62px; height: 60px;}
.certifications-logo .swiper-slide img { border-radius: 8px; background: #FFF; box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10); padding: 8px 10px; }
.certifications-logo .swiper-slide {flex-shrink: 0; height: 70px;}
.certifications-logo .swiper-slide img{width: 62px;height: 60px;object-fit: contain;border-radius: 8px;background: #FFF;box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10);padding: 8px 10px;transition: transform 0.3s ease;}
.certifications-logo .swiper-button-prev, .swiper-rtl .swiper-button-next {  left: auto;  right: 55px;  top: 25px;}
.certifications-logo .swiper-button-next, .swiper-rtl .swiper-button-prev { right: var(--swiper-navigation-sides-offset, 10px); left: auto;  top: 25px;}
.certifications-logo .swiper-button-next:after, .certifications-logo .swiper-button-prev:after, .guarantee-section .swiper-button-next:after, .certifications-logo .swiper-button-prev:after{ color: black;  font-size: 14px;  font-weight: 900;   padding: 8px 12px; background: #FFF;  border-radius: 4px;}
.certifications-logo .swiper-button-next:after, .certifications-logo .swiper-button-prev:after, .service-swiper .swiper-button-prev:after{ padding: 8px 12px;  background: #FFF; border-radius: 4px;}
.guarantee-section {  border-radius: 8px; border-bottom: 1px solid #E5E7EB;  background: #FFF; box-shadow: 0px 1px 2px -1px rgba(0, 0, 0, 0.10); padding: 10px;  height: fit-content;}
.guarantee-section .cards{border-radius: 8px; border: 1px solid #EBF2F4;  background: linear-gradient(0deg, rgba(255, 255, 255, 0.93) 0%, rgba(255, 255, 255, 0.93) 100%), #FADA7F; padding: 8px 10px;}
.banner-swiper .swiper-slide.swiper-slide-active { mask-image: linear-gradient(to right, transparent, black 100%); -webkit-mask-image: linear-gradient(to right, transparent, black 100%);}

.banner-swiper .swiper-slide-next + .swiper-slide,  .review-swiper  .swiper-slide.swiper-slide-next + .swiper-slide{ mask-image: linear-gradient(to left, transparent, black 100%); -webkit-mask-image: linear-gradient(to left, transparent, black 100%);}
/* .cert-swiper .swiper-button-prev {display: none;} */
.pro-stylist-logo img { height: 120px; width: 120px;}

.meet-the-team-swiper .swiper-slide .guarantee-section { flex: 1;  display: flex; flex-direction: column;  justify-content: center; height: 100%;}

ul.pro-social-icons{  display: flex; gap: 10px; padding-left: 0;}
ul.pro-social-icons img { width: 25px; height: 25px;}
.professional-swiper .swiper-slide img, .professional-swiper .swiper-slide .swiper-slide-next img { border-radius: 5px;}
.time-container{position: sticky; align-self: flex-start; top:16em; margin-bottom: 6em;}

/* Service page */
.service .service-tab, .pro-service-tabs { padding: 7px 12px; min-width: fit-content; border-radius: 20px; width: fit-content; color: #363636; border: 1px solid transparent; opacity: 0.6; font-family: Sora; font-size: 14px; font-weight: 400; }
.service .service-tab.active{ border: 1px solid var(--deep-blue); background: var(--white); color: var(--deep-blue); opacity: unset; font-weight: 600; }
.service .professional-tab  { border-radius: 6px; padding: 7px 12px; background: var(--cultured-white); color: #363636; font-family: Sora; font-size: 14px; font-weight: 400; opacity: 0.6; }
.service .professional-tab.active { background: var(--deep-blue); opacity: unset; }
.service-swipper, .professional-swipper { position: unset; }
.search-bar { border-radius: 6px; border: 1px solid var(--border-color); background: var(--white); padding: 5px 8px 5px 16px; width: 314px; font-size: 14px; }
.search-bar input { border: none; background: transparent }
.search-bar input:focus { outline: 0; box-shadow: none; }
.service-swipper .swiper-button-next, .service-swipper .swiper-button-prev, .certifications-logo  .product-certifications-prev, .certifications-logo .product-certifications-next{ border-radius: 4px; background: var(--cultured-white); padding: 4px; height: 24px; width: 24px; }
.services-card .card-footer { background-color: var(--ice-blue); }
 .swiper-button-next:after, .service-swipper .swiper-button-prev:after { color: black; font-size: 14px; font-weight: 900; }
.service-swipper .swiper-button-prev, .professional-swipper .swiper-button-prev { right: 44px; left: auto; }
.service-swipper .swiper-button-prev, .service-swipper .swiper-button-next, .professional-swipper .swiper-button-prev, .professional-swipper .swiper-button-next { top: 18px; }
/* .service .swiper-slide {  width: fit-content !important;} */
/* .cards-length .swiper-slide {  width: unset !important;} */
/* Professional profile styling */

.pro-subcategories-tabs  .nav-item .nav-link, #subcategory-pills-tab .nav-item .nav-link {border-radius: 6px; padding: 7px 12px; font-family: Sora; font-size: 14px; font-weight: 400; opacity: 0.6; color: #363636; }
.nav-link, .nav-link:hover{ color: var(--deep-blue);}
.pro-service-tabs .nav-item .nav-link.active {  background: var(--deep-blue);  opacity: unset;  color: var(--white);}
.pro-service-tabs .nav-item .nav-link, #subcategory-pills-tab .nav-item .nav-link.active { color: #363636;  opacity: unset;}
ul.service-social-icons img {  width: 60px;  height: 60px;}
#subcategory-pills-tab .nav-item .nav-link.active { border: 1px solid var(--deep-blue); background: var(--white); color: var(--deep-blue); opacity: unset;   font-weight: 600;   border-radius: 20px; }

.heart-fav i, .heart-fav img {cursor: pointer;}
.w-md-120px{width: 120px;}

.hours-toggle { display: flex; cursor: pointer; gap: 10px; align-items: center; }
.hours-toggle .clock-icon { margin-right: 8px; }
.hours-toggle .chevron { font-size: 12px; transition: transform 0.2s; }

/* Add this to your existing CSS */
.hours-toggle.expanded .chevron i {  transform: rotate(180deg);}
.hours-toggle .chevron i { transition: transform 0.3s ease;}

/* .hours-list { list-style: none; padding: 0; margin-top: 10px; display: none; }
.hours-list.active { display: block; } */

/* OPEN by default */
.hours-list {  list-style: none;  padding: 0; margin-top: 10px;  display: block;        }

/* CLOSED when toggled */
.hours-list.active { display: none;          }

.pro-stylist-logo img { height: 120px;  width: 120px;  border-radius: 50%;}
.hours-row { display: flex; justify-content: space-between; padding: 4px 0; font-size: 14px; }
.dot { color: #0f9d58; margin-right: 5px; }

.service-social-icons{display: flex; gap: 14px; flex-wrap: wrap;}
ul.service-social-icons  {padding-left: 0; margin-top: 2em;}
ul.service-social-icons li { border-radius: 10px; border: 1px solid #DCDDE8; background: #FFF; padding: 12px; }

.review-swiper .swiper-slide { width: 32% !important;}
.cert-swiper .swiper-slide { width: 24% !important;}
.meet-the-team-swiper img{width: 120px; height: 120px; border-radius: 50%;}

.professional-image-gallery{  display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));  gap: 16px; }
.professional-image-gallery  .image-card {  position: relative;  overflow: hidden;  border-radius: 12px;  height: 180px;  }
.professional-image-gallery  .image-card img {  width: 100%;  height: 100%; object-fit: cover;  transition: transform 0.3s ease;   border-radius: 12px;  }
.professional-image-gallery .image-card:hover img {  transform: scale(1.03);}
.professional-image-gallery  .overlay { position: absolute; inset: 0; background-color: rgba(0, 0, 0, 0.6); color: white; font-size: 28px; font-weight: bold; display: flex; justify-content: center; align-items: center; border-radius: 12px; cursor: pointer; transition: background-color 0.3s ease; }
.professional-image-gallery   .overlay:hover {  background-color: rgba(0, 0, 0, 0.75); }
.professional-image-gallery  .hidden-image { display: none; }
.professional-swiper.banner-swiper .swiper-slide.swiper-slide-next {  width: 40% !important;  height: 380px;}
.professional-swiper.banner-swiper .swiper-slide.swiper-slide-active, .professional-swiper.banner-swiper  .swiper-slide {  width: 30% !important;  height: 380px;}

/* .professional-swiper.banner-swiper .swiper-button-next:after{margin-right: 4em;}
.banner-swiper .swiper-button-prev:after{margin-left: 4em;} */


.guarantee-section .swiper-button-next:after, .review-swiper .swiper-button-next:after, .meet-the-team-swiper .swiper-button-next:after, .banner-swiper .swiper-button-prev:after, .professional-swiper.banner-swiper .swiper-button-next:after, .meet-the-team-swiper .swiper-button-prev:after {
    color: black;
    font-size: 14px;
    font-weight: 900;
    border-radius: 50%;
    left: -40px;
    width: 100%;
    height: 100%;
    align-content: center;
    text-align: center;
}

.swiper-button-prev, .swiper-button-next {
    font-size: 14px;
    font-weight: 900;
    background: #FFF;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    z-index: 1;
}


.iti__selected-flag:focus-visible { outline: unset;}

/* .guarantee-section .swiper-button-next:after, .review-swiper .swiper-button-next:after, .meet-the-team-swiper .swiper-button-next:after, .banner-swiper .swiper-button-prev:after, .professional-swiper.banner-swiper .swiper-button-next:after, .cert-swiper .swiper-button-next:after,  .meet-the-team-swiper  .swiper-button-prev:after { color: black; font-size: 14px; font-weight: 900; padding: 15px 20px; background: #FFF; border-radius: 50%; } */
.guarantee-section .swiper-button-next:after, .review-swiper .swiper-button-next:after{margin-right: 2em;}
/* .guarantee-section .swiper-button-prev:after, .review-swiper .swiper-button-prev:after, .meet-the-team-swiper .swiper-button-prev:after {display: none;} */

.time-container i.large-icon { font-size: 20px; color: #000; }
.blue-btn { border-radius: 8px; border: 1px solid var(--deep-blue); background: var(--deep-blue); color: white !important; padding: 8px 16px; cursor: pointer; }
.black-border-btn { border-radius: 8px; border: 1px solid #000; background: #F3F4F6; padding: 11px 20px; font-size: 16px; font-weight: 600; color: var(--deep-blue); }

.certificate-license-swiper img { border-radius: 4px; border: 1px solid #DCDDE8; background: #FFF;  width: 100%; padding: 12px;  height: 150px;  object-fit: cover; margin-bottom: 1em;}
body .swiper.cert-swiper {position: unset;}

.guarantee-section i.text-chat {  color: var(--white); padding-right: 5px;}
.guarantee-section .blue-button:hover i{color: var(--deep-blue) ;}

.swiper-button-next:after, .swiper-button-prev:after { font-weight: 700; font-family: swiper-icons; font-size: small; color: black;}
.further-services-modal i { border-radius: 20px; background: #FFF; padding: 7px 12px; margin-left: 5px; cursor: pointer; }

.professional-stylist img {width: 18px; height: 18px;}
.professional-stylist select.custom-select {  border: unset; }
.professional-stylist { border-radius: 20px; background: #FFFF; border: 1px solid #80808047; padding: 7px 12px; }
select.custom-select:focus-visible { outline: unset;}
.professional-stylist select { padding: 0px 10px;}

.main-heading-icon {  cursor: pointer;}
.category-checkbox input[type="checkbox"] { display: none; }
.category-checkbox {width: unset;}
.category-checkbox span { font-size: 14px;  color: #333;}
.category-checkbox input[type="checkbox"]:checked + span { border: 1px solid var(--deep-blue); padding: 7px 12px; border-radius: 20px; color: var(--deep-blue);  background-color: #f2f6ff; font-weight: 500;}

/* profile-setting */
.profile-setting .image-button {border-radius: 100px; border: 2px solid var(--white); background: var(--deep-blue);}
.profile-setting .image-button i {color: var(--white);}
.profile-setting .service-details{border-radius: 20px;background: var(--bg-color);padding:6px 11px; width: fit-content;}

.right-sidebar-menus.menu-gray-800 .menu-item .menu-link.logout{color:red;}
.right-sidebar-menus.menu-state-color .menu-item.hover:not(.here)>.menu-link:not(.disabled):not(.active):not(.here), .right-sidebar-menus.menu-state-color .menu-item:not(.here) .menu-link:hover:not(.disabled):not(.active):not(.here){color:var(--deep-blue);}

/*mega menu css*/
.mega_menu .dropdown-menu a {text-decoration: none;color: var(--black);}
.mega_menu .dropdown-menu a .d-flex { transition: all 0.5s;}
.mega_menu .dropdown-menu .col-sm-6:nth-child(1) a:hover .d-flex {background-color: var(--bs-warning-bg-subtle);}
.mega_menu .dropdown-menu .col-sm-6:nth-child(2) a:hover .d-flex {background-color: var(--bs-danger-bg-subtle);}
.mega_menu .dropdown-menu {background-color: var(--white);border-top: 1px solid var(--border-color);z-index: 9999999;}
.mega_menu .mega_menu_tabs{    color: rgba(54, 54, 54, 1);font-family: Sora;font-size: 16px;font-weight: 400;text-align: start;}
.mega_menu .mega_menu_tabs.active{border-radius: 4px; background: #F3F4F6;color: rgba(0, 0, 0, 1);}
.mega_menu .tab-content .tab-pane .category-box{border-radius: 4px;padding: 10px 16px;border: 1px solid var( --border-color);color: var(--light-black);font-family: Inter;font-size: 14px;font-weight: 400;opacity: 0.6;  cursor: pointer;transition: 0.2s ease-in-out;}
.mega_menu .tab-content .tab-pane .category-box .arrow-icon{display: none;color: var(--black);font-size: 15px;font-weight: 600;}
.mega_menu .tab-content .tab-pane .category-box:hover{color: var(--black);box-shadow: 0px 10px 15px -3px #F3F4F6, 0px 4px 6px -4px #F3F4F6;opacity: 1;}
.mega_menu .tab-content .tab-pane .category-box:hover .arrow-icon{display: block;}

.schedule-container .schedule .home-input { padding: 10px;  border-radius: 10px;border: 1px solid var(--input-border); background: var(--white);       }
.schedule-container .btn-next,.schedule-container .btn-prev {border-radius: 4px;background: var(--Light-Grey-3, var(--whisper-gray));border: none;cursor: pointer;padding: 8px 16px;  }
.schedule-container .schedule {border-radius: 8px;border: 1.127px solid var(--neutral-gray);background:var(--white);}

/* range-slider */
.slider-container .slider { -webkit-appearance: none; width: 100%;height: 12px;background:#F0F0F0 ;border-radius: 10px;outline: none;transition: background 0.3s ease;}
.slider-container .slider:focus {outline: none; }
.slider-container .slider::-webkit-slider-thumb { -webkit-appearance: none; appearance: none; width: 20px;height: 20px;border-radius: 50%; background: var(--deep-blue); /* White thumb */border: 4px solid white;/* White border around the thumb */ cursor: pointer; box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.1); /* Raised effect with shadow */transition: background 0.3s ease, border 0.3s ease, box-shadow 0.3s ease;}
/* Change color of the thumb on hover */
.slider-container .slider::-webkit-slider-thumb:hover {background: var(--deep-blue); /* Slightly darker color on hover */ }
/* Firefox specific styles */
.slider-container .slider::-moz-range-thumb {width: 20px;height: 20px;border-radius: 50%;background: var(--deep-blue); /* White thumb */  border: 4px solid white; /* White border around the thumb */cursor: pointer; transition: background 0.3s ease, border 0.3s ease; }
/* Change color of the thumb on hover for Firefox */
.slider-container .slider::-moz-range-thumb:hover {background: #ccc; /* Slightly darker color on hover */ }
.service-details{ border-radius: 8px;border: 1.127px solid #F0F0F0;background: #FFF;}

/* filter modal checkbox */
.category-checkbox {position: relative; display: inline-block;padding: 0; margin: 0; cursor: pointer;width:100%;}
.category-checkbox input[type="radio"] { display: none;}
.category-checkbox .top-rated-card { background: none; display: inline-block; width: 100%; box-shadow: 0px 1.127px 2.253px 0px rgba(0, 0, 0, 0.05) ; padding: 7px 12px; border: 2px solid transparent; transition: all 0.3s ease;border-radius: 6px; color: var(--deep-blue); font-family: Sora;font-size: 12px;font-weight: 400;}
.category-checkbox p .span-class {color: var(--gray); font-family: Sora;font-size: 12px;font-weight: 400;}
.category-checkbox input[type="radio"]:checked + .top-rated-card {box-shadow: 0px 1.127px 2.253px 0px rgba(0, 0, 0, 0.05);color: var(--deep-blue); font-weight: 600; border: 2px solid var(--deep-blue);  background: var(--white);}
/* .category-checkbox input[type="radio"]:checked + .top-rated-card , .category-checkbox .top-rated-card{padding:0 9px;} */
.category-checkbox input[type="radio"]:checked + .top-rated-card , .category-checkbox .top-rated-card{padding:16px 0;}
.category-checkbox input[type="radio"]:checked + p .span-class {color: var(--light-black);opacity: 0.6;font-weight: normal;}
.filter-modal .modal-footer .add-btn {font-size: 13px;font-weight: 500;padding: 10px 16px;color: var(--snow-white);}
.filter-modal .modal-footer .trans-button {font-size: 13px;font-weight: 500;padding: 10px 16px;border-radius: 6px;border: 1px solid rgba(0, 0, 0, 0.10);  color: var(--black);}

.category-checkbox input[type="radio"]:checked  ~ * body .calender-date{color: var(--deep-blue);}

.schedule [data-day="Sunday"]:has(input:checked) #closed-sunday{display:none;}
.date-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }
.date-header button {border-radius: 4px;background: var(--cultured-white);padding: 4px;   height: 26px;  width: 24px;border: 0;}
.date-header select:focus {outline: none;}
.date-box strong {color: var(--deep-blue); text-align: center;  font-size: 20px; font-weight: 600;}
.add-services-modal .modal-dialog.modal-dialog-centered {max-width: 610px;}
body .professional-name{ color: var(--black);    text-align: center; font-family: Sora;  font-size: 14px;  font-weight: 600;}
body .professional-profession{color: var(--light-black);text-align: center;font-family: Inter;font-size: 12px;}
body .calender-date{color: var(--deep-blue);text-align: center;font-family: Inter;font-size: 20px;font-weight: 600;}
body .calender-day{color: var(--light-black);text-align: center;font-family: Inter;font-size: 10px;font-weight: 400;}
body .top-rated-card.time-slots{color: var(--black);font-family: Inter;font-size: 14px;font-weight: 500;}

/* filter modal checkbox */
.reschedule-booking .category-checkbox, .professionalfilterModal  .category-checkbox {width: unset;}
.reschedule-booking .category-checkbox input, .professionalfilterModal .category-checkbox input { display: none;}
.reschedule-booking .category-checkbox span, .professionalfilterModal  .category-checkbox span{ display: inline-block; padding: 7px 12px; border: 1px solid transparent; transition: all 0.3s ease;border-radius: 20px; color: var(--light-black); font-family: Sora;font-size: 12px;font-weight: 400;;background: var(--whisper-gray);}
.reschedule-booking .category-checkbox input[type="checkbox"]:checked+span { color: var(--deep-blue); font-weight: 600; border: 1px solid var(--deep-blue);  background: var(--white);}

/* booking calender  */
.calender-box {border-right: 1px solid var(--border-color);background: var(--white);border-top: 1px solid var(--border-color); border-left: 1px solid var(--border-color); border-bottom: 1px solid var(--border-color);}
.calender-box span.flatpickr-day.selected.startRange{background-color: var(--deep-blue); border: 1px solid var(--deep-blue);}
.calender-box span.flatpickr-day.selected.endRange{background-color: var(--deep-blue); border: 1px solid var(--deep-blue);}
.calender-box  span.flatpickr-day.inRange {background: rgba(0, 106, 160, 0.13);border: 0;color:var(--black);}
.calender-box .flatpickr-calendar.rangeMode.animate.inline {border: 0; box-shadow: none;}
.booking-section .schedule-container  {background-color: white; padding: 20px 0 0 20px; border-bottom: 1px solid var(--border-color); overflow-y: scroll; overflow-x: hidden;    height: 460px;}

.manage-pro-holidays .booking-section .schedule-container {background-color: white; padding: 20px; border-bottom: 1px solid var(--border-color); height: auto;}

.new-customer-cal-view .customer-calender {background-color: rgb(249 249 249); padding: 20px; border-bottom: 1px solid var(--border-color); height: auto;}



.schedule-container .calendar-header .btn-next, .schedule-container .calendar-header  .btn-prev , .schedule-container .calendar-header p {border-radius: 4px;border: 1px solid var(--neutral-gray);background-color: transparent; padding: 8px 16px;}

.user-option { display: inline-flex; align-items: center; padding: 3px 12px 4px 10px; border: 2px solid #ccc; cursor: pointer; transition: all 0.3s ease; border-radius: 20px; border: 1px solid var(--Light-Grey-2, #E5E7EB); background:var(--white); }
.user-option input { display: none; }
.user-option img {   width: 30px;  height: 30px;  border-radius: 50%; }
.user-option span { font-weight: 500;  color: #999; }
.user-option .checkmark { display: none;  margin-left: 8px; color: var(--deep-blue);  font-size: 16px; }
.user-option input:checked + label {  border-color: var(--deep-blue); background-color:var(--white); }
.user-option input:checked + label span {  color: var(--deep-blue); font-weight: 700; }
.user-option input:checked + label .checkmark {   display: inline;  color:var(--white);  padding: 3px 5px; font-size: 7px;  background-color: var(--deep-blue); border-radius: 50%; }
.user-option:has(input:checked) {border: 1px solid var(--deep-blue);}

/* reschedule-booking-modal modal checkbox */
.reschedule-booking-modal  .category-checkbox {position: relative; display: inline-block;padding: 0; margin: 0; cursor: pointer; width: unset;}
.reschedule-booking-modal .category-checkbox input[type="checkbox"] { display: none;}
.reschedule-booking-modal  .category-checkbox span { display: inline-block; padding: 7px 12px; border: 1px solid transparent; transition: all 0.3s ease;border-radius: 20px; color: var(--light-black); font-family: Sora;font-size: 12px;font-weight: 400;;background: var(--whisper-gray);}
.reschedule-booking-modal  .category-checkbox input[type="checkbox"]:checked+span { color: var(--deep-blue); font-weight: 600; border: 1px solid var(--deep-blue);  background: var(--white);}

/*loader*/
.logo-loader .text {font-size: 45px;font-weight: bold;color: var(--black);font-family: 'sora';}
.logo-loader { display: flex; justify-content: center; align-items: center; background-color: var(--whisper-gray); position: fixed; top: 0; left: 0; height: 100vh; width: 100vw; z-index: 9999999; }
.logo-loader .logo-container {  display: flex;  align-items: center;  justify-content: center; }
.logo-loader .circle {  width: 100px;  height: 80px;  animation: spin 2s linear infinite;  margin: 0;}

@keyframes spin {
    0% {   transform: rotate(0deg); }
    100% {  transform: rotate(360deg); }
}

/* booking calender css */
.calendar-icon { border-radius: 4.552px; background: var(--whisper-gray); padding: 13px 20px; }
#calendar { width: 100%;overflow-x: scroll; overflow-y: hidden;}
#calendar table {width: 100%;  margin-top: 20px;border-collapse: collapse;}
#inline-calendar span.flatpickr-day.selected.startRange {background-color: var(--deep-blue);}
#calendar tr>th select, #calendar tr>th:not(:first-child) span {border: 0;outline: 0;height: 100%;display: flex;padding: 15px; align-items: center;border-radius: 10px;justify-content: center;box-shadow: 0 0 26px #e7e7e7;}
#calendar td:hover {cursor: pointer;}
#calendar tr>td:not(:first-child) { padding: 0;  height: 154px; min-width: 56px; max-width: 58px;user-select: none; box-shadow: -1px 1px 1px 0px #E0E0E0 inset;}
#calendar tr>td {position: relative;}
.popup {left: 0;z-index: 1;width: 427px;height: 180px;color:var(--black);font-size: 14px; position: absolute;padding: 20px 10px;background: #fff; bottom: calc(200px - 380px);   filter: drop-shadow(0px 0px 26px rgba(48, 81, 138, 0.12));}
.popup .container {gap: 10px; display: flex;flex-direction: column;}
.popup .btnBooking {  width: 100%;  display: block;text-align: center;text-decoration: none;}
#calendar tr>td:first-child { padding: 5px 0 5px 0;}
#calendar tr>td:first-child * {margin: 0;}
#calendar .title { width: 100%;display: flex;min-width: 100px;background:var(--whisper-gray);flex-direction: column;background: var(--white, #FFF);padding-left: 20px; box-shadow: -1px 0px 0px 0px #E0E0E0 inset;}
.reservation {top: -2px;left: -3px; z-index: 1;display: flex;overflow: hidden;position: absolute;flex-direction: column;height: calc(100% + 4px);justify-content: space-between;border-radius: 4px;background: rgba(14, 165, 233, 0.10);border-left: 5px solid #0EA5E9}
.reservation>* {padding: 0;text-align: left;list-style-type: none;}
.reservation>h4 { color: #0369A1;;margin: 20px 0 0 20px;}
.reservation>ul {gap: 14px; display: flex; flex-direction: column;margin: 0 0 20px 20px;}
.reservation>ul li {gap: 10px;display: flex;color: #4A4A4A; font-size: 12px;align-items: center;}
.flex-align-space-btw {  gap: 12px;display: flex;  align-items: center;justify-content: space-between;}
.header-btn {font-size: 15px;color: #4A4A4A;cursor: pointer;border-radius: 100px;box-shadow: 0 0 26px #e7e7e7;}
.header-btn:not(.calendar-today) {padding: 10px 20px;}
.header-btn:not(.calendar-today) i {color: #7BB541;}
.calendar-coursts-payable a {text-decoration: none;        }
.calendar-today i {  padding: 13px;  border: 1px solid #E8E8E8;}
.calendar-today> :first-child {  border-width: 0 1px 0 0; }
.calendar-today> :last-child { border-width: 0 0 0 1px;}
.calendar-date input { border: 0; outline: 0; width: 100vw; max-width: 200px;}
.calendar-date {position: relative;}
.calendar-coursts-payable .dropdown-menu {border: 0;box-shadow: 0 7px 7px #d3d3d3;}
#calendar .weekend-class .title { background-color: var(--snow-white);}
#calendar tbody td.weekend-class { background-color: var(--snow-white); }
#calendar tbody td.today-class {  background-color: #EFF6FF;}
#calendar .today-class .title { background-color: #EFF6FF; }
#calendar .title p {color: #71717A;font-family: Inter;font-size: 10px;font-weight: 700;text-transform: uppercase;margin-bottom: 0;}
#calendar .title h4 {color: var(--black);font-family: Inter;font-size: 22px;font-weight: 500;}
.width-1-slot{width: 167px;}
.customer-calender #calendar .title{min-width: 140px;}
.customer-calender #calendar  .width-1-slot {width: 189px;}

.top-rated-card .card-header .fav-icon i { color: var(--red); font-size: 18px;}
.top-rated-card .card-header .fav-icon {cursor: pointer;}
.top-rated-card .card-body .review-icon , .step-card .review-icon{color: var(--black);}
.form-hide-box{display: none;}
.family .instagram-gradient {background: linear-gradient(45deg, #fcb045, #fd1d1d, #833ab4); -webkit-background-clip: text; -webkit-text-fill-color: transparent;font-size: 20px;align-content: center;}
#add-category-button{display: none;}
#add-subcategory-button{display: none;}
.form-add-services .upload-icon{font-size: 20px;}

/* Customer Dashboard */
a.profile-delete-btn { padding: 8px 16px; border: 1px solid darkred; background: darkred; color: #FFF; border-radius: 8px; }
div#kt_app_header_wrapper { position: fixed; width: 100%; z-index: 9; }
.customer_dashboard.profile-setting .friends-cards { position: sticky; top: 16em;}
.step-card i , .search_box .file-icon , .breadcrumbs .right-arrow{color: var(--light-black);}
.card-box.step-card{height: unset;position: sticky;top: 0;}

/* admin dashboard */
.business-home-sec div#chartLegend {margin-left: 30px;}
.business-home-sec .bookingchart{width: 300px; height: 300px;}
.card-details.edit-pkg input:focus{border: 1px solid var(--bs-gray-300);}

/* Modal positioning fixes */
.modal.card-details {  z-index: 1055;}
.modal.card-details .modal-dialog { margin: 1.75rem auto;  max-height: calc(100vh - 3.5rem);}
.modal.card-details .modal-content { max-height: calc(100vh - 3.5rem);  overflow-y: auto;}
.modal.card-details .modal-dialog.modal-dialog-centered { min-height: calc(100vh - 3.5rem);  display: flex;   align-items: center;}
.modal.card-details .modal-dialog.modal-dialog-centered::before { content: '';  display: block;  height: calc(50vh - 50%);  height: -webkit-min-content;  height: -moz-min-content;  height: min-content;}
.modal-backdrop { z-index: 1050;}

/* Select2 dropdown positioning in modals */
.select2-container--default .select2-dropdown { z-index: 1060;}
.modal .select2-container {  width: 100% !important;}
.modal .select2-container .select2-selection--multiple {  min-height: 38px;  border: 1px solid #d1d3e2;  border-radius: 0.35rem;}
.modal .select2-container .select2-selection--multiple .select2-selection__choice { background-color: var(--white);  border: 1px solid var(--deep-blue); color: var(--deep-blue);   border-radius: 0.25rem;  padding: 0 0.5rem;  margin: 0.25rem 0.25rem 0 0;}

#personal-info-form .image-input-empty  {background-image: url('../images/image_input_holder.png');}

label { margin-bottom: 5px;}
.section-wrapper.gray-card {   border: 1px solid #80808073;  border-radius: 10px;  padding: 20px;  margin-bottom: 2em;}
button.delete-block { border: 1px solid #FFF; background: darkred; color: #FFF; border-radius: 8px; padding: 10px 15px; }
.modal {   z-index: 99999999999;}

/* .cms-sec-title {   border-bottom: 1px solid #80808073;  margin-bottom: 2em;  padding-bottom: 1em;} */
.share-certificates .upload-box, .file-upload-group .upload-box{ height: 250px;  width: 100%;}
.share-certificates .add-file {  border-radius: 4px;   background: var(--charcoal-black);  width: 120px;   margin-bottom: 10px; height: unset; padding: unset;}
.share-certificates p{font-size: 14px;}
.share-certificates .upload-box img { width: 100px;  height: 0px;}

/* Admin Dashboard Styling */

[data-kt-app-layout=dark-sidebar] .app-sidebar .menu>.menu-item .menu-link .menu-title { color: var(--deep-blue); margin-left: 6px; }
[data-kt-app-layout=dark-sidebar] .app-sidebar { background: #FFF; }
[data-kt-app-layout=dark-sidebar] .app-sidebar .menu>.menu-item .menu-link.active, [data-kt-app-layout=dark-sidebar] .app-sidebar .menu>.menu-item .menu-link:hover, [data-kt-app-layout=dark-sidebar] .app-sidebar .menu>.menu-item .menu-link:hover i {background-color: var(--deep-blue); color: var(--white); }
[data-kt-app-layout=dark-sidebar] .app-sidebar .menu>.menu-item .menu-link.active i { color: var(--white); }
[data-kt-app-layout=dark-sidebar] .app-sidebar .menu>.menu-item .menu-link i {color: var(--deep-blue);}
[data-kt-app-layout=dark-sidebar] .app-sidebar .app-sidebar-logo { border: unset; }

.date-item { border-radius: 6px; border: 1px solid #F0F0F0; background: #FFF; box-shadow: 0px 1.127px 2.253px 0px rgba(0, 0, 0, 0.05); padding: 12px 24px; }
div#dateGrid { display: flex ; gap: 10px; width: 100%; }
/* div#timeGrid { display: flex ; flex-wrap: wrap; gap: 10px; }  */
.date-item.selected, .time-item.selected { border-radius: 6px; border:2px solid #020C87 !important; background: #FFF; }
.time-item, div#selectionDisplay { border-radius: 6px; border-radius: 6px; border: 1px solid #F0F0F0; background: #FFF; box-shadow: 0px 1.127px 2.253px 0px rgba(0, 0, 0, 0.05); padding: 8px 3em; }
select#monthSelect { border: unset; margin-block: 1em; font-size: 14px; font-style: normal; font-weight: 600; }

.customer_dashboard .category-checkbox:hover { border-color: #d1d5db;}
.customer_dashboard .category-checkbox input[type="radio"] {appearance: none; }
.customer_dashboard .category-checkbox{border-radius: 6px;border: 1px solid #F0F0F0; background: #FFF; box-shadow: 0px 1.127px 2.253px 0px rgba(0, 0, 0, 0.05); padding: 12px; width: 100%;}
.customer_dashboard .category-checkbox .span-class-service{font-size: 13px; color: #36363699; background-color: #FFF;}
.customer_dashboard .category-checkbox p { display: flex; flex-direction: column; margin: 0; font-size: 16px; font-weight: 600; color: #1f2937; }
.customer_dashboard .category-checkbox:has(input[type="radio"]:checked) { border-radius: 6px; border:2px solid #020C87; background: #FFF; box-shadow: 0px 1.127px 2.253px 0px rgba(0, 0, 0, 0.05); width: 100%; padding: 12px; }

body:not([data-kt-app-header-fixed=true])[data-kt-app-sidebar-fixed=true][data-kt-app-sidebar-push-header=true]
.app-header.admin-sidebar-changes,  body:not([data-kt-app-header-fixed=true])[data-kt-app-sidebar-sticky=on][data-kt-app-sidebar-push-header=true]
.app-header.admin-sidebar-changes {
 margin-left: calc(var(--bs-app-sidebar-width) + var(--bs-app-sidebar-gap-start, 0px) + var(--bs-app-sidebar-gap-end, 0px)); }
[data-kt-app-sidebar-fixed=true] .app-wrapper.admin-padding { margin-left: calc(var(--bs-app-sidebar-width) + var(--bs-app-sidebar-gap-start, 0px)
 + var(--bs-app-sidebar-gap-end, 0px));}

body:not([data-kt-app-header-fixed=true])[data-kt-app-sidebar-fixed=true][data-kt-app-sidebar-push-header=true] .app-header,
body:not([data-kt-app-header-fixed=true])[data-kt-app-sidebar-sticky=on][data-kt-app-sidebar-push-header=true]
.app-header, [data-kt-app-sidebar-fixed=true] .app-wrapper{   margin-left: unset; }
body:not([data-kt-app-header-fixed=true])[data-kt-app-sidebar-fixed=true][data-kt-app-sidebar-push-header=true] .app-header, body:not([data-kt-app-header-fixed=true])[data-kt-app-sidebar-sticky=on][data-kt-app-sidebar-push-header=true] .app-header, [data-kt-app-sidebar-fixed=true] .app-wrapper {
 margin-left: unset; }

.app-container {  padding-left: unset !important;  padding-right: unset !important;}
div#weekDaysContainer { border-radius: 8px; border: 1.127px solid #F0F0F0;  background: #FFF;  padding: 20px;}
.day-row{margin-bottom: 2em;}
.modal { --bs-modal-width: 530px;}
.professional-swipper .swiper-slide { width: 100% !important;}

/* Admin-sidebar styling  */
.app-container {   padding-left: unset !important; padding-right: unset !important; }
div#weekDaysContainer {   border-radius: 8px;  border: 1.127px solid #F0F0F0;  background: #FFF; padding: 20px;}
.day-row{margin-bottom: 2em;}
.modal { --bs-modal-width: 530px;}
.professional-swipper .swiper-slide {width: 100% !important;}
.admin-padding section.second-sec{ display: none; }
#kt_app_header_wrapper.admin-sidebar2 { position: fixed; width: calc(100% - 265px); z-index: 9;}
.admin-sidebar2 .discount-header, .admin-sidebar2 nav.navbar.navbar-expand-lg.bg-white.header {  padding-inline: 2em;}
.admin-sidebar2 .header-items { display: none;}
body .admin-padding #kt_app_main {margin-inline: 2em; margin-top: -7em; margin-bottom: 2em;}

div#kt_app_sidebar_mobile_toggle { background: #FFF;  color: #FFF; z-index: 999;  position: fixed;}
div#kt_app_sidebar_mobile_toggle i { font-size: 20px;}
.admin-sidebar2 .discount-header, .admin-sidebar2 .navbar-brand,  .admin-sidebar2 .second-sec{display: none;}
.app-sidebar-logo { --bs-app-header-height: 75px;; }
.app-sidebar-menu.overflow-hidden.flex-column-fluid.admin-sidebar { margin-top: 0;}
.form-add-category .image-input-empty{background-image: none; width: 160px; display: inline-block;}

.card.white-box.friends-cards  img.customer_profile{ width: 250px; height: 250px; border-radius: 50%; }

.customer_dashboard.profile-setting .friends-cards {  position: sticky; top: 11em;}

/* Manage Holidays */

.manage-pro-holidays #responsiveTable tr {  transition: all 0.3s ease; box-shadow: 0px 1px 3px 0px #0000001A;  border-radius: 8px; height: -webkit-fill-available;   background: #FFF;   border: 0; }
.manage-pro-holidays #responsiveTable.manage-holiday td{ width: 30%;  border: 0; }
.manage-pro-holidays #responsiveTable.manage-holiday.vat-managment-table tr td:first-child { width: 50%; border: 0; }

/* Pagination */

.pagination li.page-item.active {
    --bs-pagination-active-color: #FFF;
    --bs-pagination-hover-bg: var(--deep-blue);
    --bs-pagination-color: #FFFF;
    --bs-pagination-active-bg: var(--deep-blue);
}

.pagination li.page-item { --bs-pagination-hover-color: var(--deep-blue);}
/* .certificate-license-swiper img {  width: 160px;   height: 130px;} */

/* Customer header counts */

/* .notification-count, span.email-count, .wish-count, .cart-count { position: absolute;  font-size: 10px; font-weight: 800; color: #6b7280ab;} */

.notification-count, .email-count, .wish-count, .cart-count { position: absolute; font-size: 10px; background: var(--deep-blue); color: #FFF; border-radius: 50%; width: 15px; height: 15px; text-align: center; }

.notification-count {  top: -16px;  right: -11px;}
.email-count {  right: -9px; top: -19px;}
.wish-count {   top: -10px; right: -7px;}
.cart-count {  right: -13px;   top: -17px;}

.professional-swiper.banner-swiper .swiper-slide img {  background-size: cover;   height: 370px ;  background-repeat: no-repeat;}

/* QA Fixes */

.icons svg:hover {   width: 30px; height: 30px;}
.flatpickr-input { width: 100%;  cursor: pointer;}

/* ========================================
   CONSOLIDATED DASHBOARD STYLES
   ======================================== */

/* Search Loading Indicators - Used across 30+ files */
.search_input.searching {
    opacity: 0.7;
    pointer-events: none;
    background-image: url("data:image/svg+xml,%3csvg width='20' height='20' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M9 2a7 7 0 1 0 0 14 7 7 0 0 0 0-14zM2 9a7 7 0 1 1 14 0 7 7 0 0 1-14 0z' fill='%23999'/%3e%3cpath d='m13 13 4 4' stroke='%23999' stroke-width='2' stroke-linecap='round'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    animation: pulse 1.5s infinite;
}

/* Loading Row Styling - Used across 30+ files */
.loading-row td {
    text-align: center;
    padding: 2rem;
}

/* Validation Errors - Used across 30+ files */
.error {
    color: #dc3545 !important;
    display: block;
    margin-top: 5px;
}

/* Pulse Animation - Used across 15+ files */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Loading Spinner - Used across 15+ files */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.loading-spinner {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    border: 0.25em solid #f3f3f3;
    border-top: 0.25em solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Dot Styling - Used across 20+ files */
.dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}

.dot.all {
    background-color: #4B5563;
}

/* Search Results Info - Used across 10+ files */
.search-results-info .alert {
    border-left: 4px solid #0d6efd;
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

/* Search Functionality - Used across 10+ files */
.cursor-pointer {
    cursor: pointer;
}

.search-bar {
    position: relative;
}

.search-bar .search-icon {
    color: #6c757d;
}

.search-clear-icon {
    position: absolute;
    right: 12px;
    color: #6c757d;
    cursor: pointer;
    font-size: 14px;
    transition: color 0.3s ease;
}

.search-clear-icon:hover {
    color: #dc3545;
}

/* Hide/show animation for search results */
tbody tr {
    transition: opacity 0.3s ease;
}

tbody tr.hidden {
    opacity: 0;
    pointer-events: none;
}

/* No results message styling */
#no-results-message {
    background-color: #f8f9fa;
    border-radius: 8px;
    margin: 20px 0;
}

/* Search highlight styling */
.search-highlight {
    background-color: #fff3cd;
    padding: 1px 2px;
    border-radius: 2px;
    font-weight: 500;
}

/* Portfolio expand button styling */
.expand-portfolio-btn {
    transition: transform 0.2s ease;
}

.expand-portfolio-btn:hover {
    transform: scale(1.05);
}

.expand-portfolio-btn img {
    transition: filter 0.3s ease;
}

.expand-portfolio-btn:hover img {
    filter: brightness(0.3) !important;
}

.hidden-portfolio {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Map Styling - Used in professional profile */
#map {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

#map:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* Favorite Icon Styling */
.favorite-icon {
    cursor: pointer;
}

.favorite-icon i {
    font-size: 1.5rem;
}

.favorite-icon i.fas.text-danger {
    color: #e91e63 !important;
}

/* Service Search Styling */
#service-search {
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 14px;
    width: 250px;
    transition: border-color 0.3s ease;
}

/* Card Header Styling - Used in refund detail */
.card-header {
    padding: 0 10px !important;
    display: flex !important;
    align-items: center !important;
}

.card-header h5 {
    font-size: 20px;
}

.card-header i {
    font-size: 20px;
    color: black;
}

/* FullCalendar Styling - Used in business booking */
#booking-calendar {
    font-family: 'Sora', sans-serif;
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

#booking-calendar .fc {
    font-family: 'Sora', sans-serif;
}

#booking-calendar .fc-timegrid-slot {
    height: 60px !important;
    border: 1px solid #e5e7eb !important;
}

#booking-calendar .fc-timegrid-col {
    min-width: 60px !important;
}

#booking-calendar .fc-timegrid-axis {
    width: 80px !important;
    background: #f9fafb;
    border-right: 2px solid #e5e7eb !important;
}

#booking-calendar .fc-timegrid-axis-cushion {
    padding: 8px 12px;
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
}

#booking-calendar .fc-col-header-cell {
    background: #f9fafb;
    border: 1px solid #e5e7eb !important;
    padding: 12px 8px;
    font-weight: 600;
    color: #374151;
}

#booking-calendar .fc-daygrid-day,
#booking-calendar .fc-timegrid-col {
    border: 1px solid #e5e7eb !important;
}

#booking-calendar .fc-event {
    border-radius: 6px !important;
    border: none !important;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: 500;
    margin: 1px;
}

#booking-calendar .fc-event-title {
    font-weight: 500;
    overflow: hidden;
    font-size: 10px;
    text-overflow: ellipsis;
}

#booking-calendar .fc-timegrid-event {
    border-radius: 4px !important;
    margin: 1px 2px;
}

#booking-calendar .fc-now-indicator-line {
    border-color: #ef4444;
    border-width: 2px;
}

#booking-calendar .fc-now-indicator-arrow {
    border-left-color: #ef4444;
    border-width: 6px;
}

#booking-calendar table {
    width: 100% !important;
    table-layout: fixed !important;
}

#booking-calendar .fc-scrollgrid {
    border: 1px solid #e5e7eb !important;
    border-radius: 8px;
    overflow: hidden;
}

#booking-calendar .fc-scrollgrid-section>* {
    border-left: 0 !important;
    border-right: 0 !important;
}

#booking-calendar .fc-scrollgrid-section-header>td {
    border-bottom: 2px solid #e5e7eb !important;
}

#booking-calendar .fc-timegrid-slot-minor {
    border-top: 1px dashed #e5e7eb !important;
}

#booking-calendar .fc-timegrid-slot-major {
    border-top: 1px solid #d1d5db !important;
}

#booking-calendar .fc-day-today {
    background-color: #fef3c7 !important;
}

.fc-timegrid-event .fc-event-time {
    font-size: 10px;
}

/* Responsive adjustments for FullCalendar */
@media (max-width: 768px) {
    #booking-calendar .fc-timegrid-axis {
        width: 60px !important;
    }
    #booking-calendar .fc-event {
        font-size: 11px;
    }
}

/* Profile Settings Specific Styles */
.gray-card {
    border-radius: 10px;
    border: 1px solid #DCDDE8;
    background: #FFF;
    padding: 25px;
}

.time-picker-calendar label.days {
    display: flex;
    height: 44px;
    gap: 10px;
    align-items: center;
}

.upload-box {
    padding: 30px;
    cursor: pointer;
    border-radius: 10px;
    border: 2px dashed #ccc;
    background: #f9f9f9;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 10px;
    text-align: center;
    transition: background 0.3s ease;
    position: relative;
}

.upload-box:hover {
    background-color: #eee;
}

.upload-box img {
    width: 60px;
    height: 40px;
}

.add-file {
    display: block;
    font-size: 18px;
    margin-top: 10px;
    color: #555;
}

.preview-container {
    display: flex;
    flex-wrap: wrap;
    margin-top: 15px;
    gap: 15px;
}

.preview-box {
    width: 160px;
    height: 95px;
    border: 1px solid #ddd;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
    background-color: #fff;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}

.preview-box img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.remove-image,
.remove-file {
    position: absolute;
    top: 5px;
    right: 8px;
    background: red;
    color: white;
    border: none;
    border-radius: 50%;
    width: 22px;
    height: 22px;
    font-size: 14px;
    line-height: 20px;
    cursor: pointer;
    text-align: center;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.upload-cert-btn {
    padding: 7px 20px;
    border-radius: 20px;
    background: #020C87;
    color: #FFF;
}

.cert-excep {
    display: flex;
    width: 200px;
    align-items: center;
    gap: 8px;
    margin-bottom: 5px;
}

.addMoreBtn {
    border-radius: 10px;
    border: 1px dashed #020C87;
    background: #FFF;
    padding: 15px;
    margin-block: 2em;
    width: 100%;
}

.start-time .flatpickr-input,
.end-time .flatpickr-input,
.start-time1 .flatpickr-input,
.end-time1 .flatpickr-input {
    border-radius: 10px;
    border: 1px solid #DCDDE8;
    background: #FFF;
    padding: 19px !important;
    width: 120px !important;
}

.start-time {
    display: none;
}

.blue-btn {
    border-radius: 10px;
    background: #020C87;
    padding: 18px 50px;
    text-align: center;
    color: #FFF;
    border: 1px solid #FFF;
    width: 100%;
}

button.delete-block {
    border: 1px solid #FFF;
    background: darkred;
    color: #FFF;
    border-radius: 20px;
    padding: 10px 15px;
}

button.add-custom-holiday-btn {
    border-radius: 8px;
    border: 1px solid #F0F0F0;
    padding: 8px 14px;
    color: #020C87;
    font-size: 16px;
    font-weight: 600;
    background: #FFF;
    margin-top: 1em;
}

.exception-textarea {
    display: none;
}

.exception-checkbox:has(input:checked) .exception-textarea {
    display: block;
}

.exception-checkbox.expanded .exception-textarea {
    display: block;
}

.upload-box.disabled {
    opacity: 0.5;
    pointer-events: none;
}

.time-picker-calendar2 {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.time-picker-calendar2 .checked-time {
    display: none;
}

.time-picker-calendar2:has(input:checked) .closed-time {
    display: none;
}

.time-picker-calendar2:has(input:checked) .checked-time,
.time-picker-calendar2 .closed-time {
    display: flex;
    align-items: center;
    justify-content: end;
    gap: 10px;
}

.fieldlabels {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.form-border {
    border-top: 1px solid #DCDDE8;
    padding-top: 20px;
    margin-top: 20px;
}

.blue-text {
    color: #020C87;
}

.HolidayModal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    align-items: center;
    justify-content: center;
}

.HolidayModal .modal-content {
    background-color: #fefefe;
    padding: 20px;
    border-radius: 10px;
    width: 400px;
    max-width: 90%;
    margin: 10% auto;
}

.HolidayModal .close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.HolidayModal .close:hover {
    color: black;
}

.HolidayModal input {
    width: 100%;
    padding: 10px;
    margin: 10px 0;
    border: 1px solid #DCDDE8;
    border-radius: 5px;
}

.HolidayModal label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

input[type="checkbox"] {
    margin-right: 8px;
}

.checkmark {
    font-weight: 500;
}

.time-picker-range2 {
    display: flex;
    align-items: center;
}

.checked-time {
    display: flex;
    align-items: center;
    gap: 10px;
}

.closed-time {
    display: flex;
    align-items: center;
}

.closed-time p,
.time-picker-calendar p {
    font-size: 14px;
}

input[type="file"] {
    display: none;
}

.preview-item {
    position: relative;
    display: inline-block;
}

.remove-preview {
    position: absolute;
    top: -5px;
    right: -5px;
    background: red;
    color: white;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.certificate-card {
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.certificate-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.certificate-expandable:hover {
    background-color: #f8f9fa;
}

.cursor-pointer {
    cursor: pointer !important;
}

.certificate-image {
    transition: transform 0.2s ease;
}

.certificate-image:hover {
    transform: scale(1.1);
}

.certificate-toggle-icon {
    transition: transform 0.3s ease;
}

.certificate-toggle-icon.rotated {
    transform: rotate(180deg);
}

.certificate-exception-details {
    animation: slideDown 0.3s ease;
}

.certificate-rejection-details {
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ========================================
   MODAL TEMPLATE STYLES
   ======================================== */

/* Date/Time Selection Styles - Used in multiple modals */
.date-item.disabled {
    background-color: #eee;
    color: #999;
    pointer-events: none;
    cursor: not-allowed;
}

.date-item {
    border-radius: 6px;
    border: 1px solid #F0F0F0;
    background: #FFF;
    box-shadow: 0px 1.127px 2.253px 0px rgba(0, 0, 0, 0.05);
    padding: 12px 24px;
    text-align: center;
}

div#dateGrid {
    display: flex;
    gap: 10px;
    width: 100%;
}

div#timeGrid {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.date-item.selected,
.time-item.selected {
    border-radius: 6px;
    border: 2px solid #020C87 !important;
    background: #FFF;
}

.time-item,
div#selectionDisplay {
    border-radius: 6px;
    border: 1px solid #F0F0F0;
    background: #FFF;
    box-shadow: 0px 1.127px 2.253px 0px rgba(0, 0, 0, 0.05);
    padding: 8px 3em;
}

.time-item {
    width: 104px;
}

.time-item.disabled {
    opacity: 0.5;
    pointer-events: none;
    background-color: #e0e0e0;
    color: #999;
    cursor: not-allowed;
}

select#monthSelect {
    border: unset;
    margin-block: 1em;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
}

/* Category Checkbox Styles - Used in multiple modals */
.category-checkbox:hover {
    border-color: #d1d5db;
}

.category-checkbox input[type="radio"] {
    appearance: none;
}

.category-checkbox {
    border-radius: 6px;
    border: 1px solid #F0F0F0;
    background: #FFF;
    box-shadow: 0px 1.127px 2.253px 0px rgba(0, 0, 0, 0.05);
    padding: 12px;
    width: 100%;
}

.category-checkbox .span-class-service {
    font-size: 13px;
    color: #36363699;
    background-color: #FFF;
}

.category-checkbox p {
    display: flex;
    flex-direction: column;
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
}

.category-checkbox:has(input[type="radio"]:checked),
.date-item.today {
    border-radius: 6px;
    border: 2px solid #020C87;
    background: #FFF;
    box-shadow: 0px 1.127px 2.253px 0px rgba(0, 0, 0, 0.05);
    width: 100%;
    padding: 12px;
}

/* Button Styles - Used in multiple modals */
.cancel-btn {
    padding: 8px 14px;
    background: transparent;
    color: black;
    font-size: 14px;
    cursor: pointer;
    border-radius: 8px;
    border: 1px solid var(--neutral-gray);
}

.save-btn {
    padding: 5px 16px;
    font-family: Inter;
    color: var(--white);
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    border-radius: 8px;
    background: var(--deep-blue);
    border: 1px solid transparent;
}

/* Professional Selection Styles - Used in booking modals */
.professional-option {
    cursor: pointer;
    transition: all 0.3s ease;
}

.professional-option:hover {
    transform: translateY(-2px);
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
}

.professional-option input[type="checkbox"] {
    display: none;
}

.professional-option:has(input[type="checkbox"]:checked) {
    border: 2px solid #020C87 !important;
    background: rgba(2, 12, 135, 0.05);
}

.professional-option:has(input[type="checkbox"]:checked) .professional-name {
    color: #020C87 !important;
    font-weight: 600;
}

#selected-professionals {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

#selected-professionals .badge {
    font-size: 0.875rem;
    padding: 8px 12px;
    display: inline-flex;
    align-items: center;
}

#selected-professionals .btn-close {
    font-size: 0.7rem;
    padding: 0;
    margin: 0;
    width: 16px;
    height: 16px;
}

/* Modal Loader Styles - Used in booking modals */
.modal-loader-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    border-radius: 0.375rem;
}

.modal-loader-overlay .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Form Control Focus Styles - Used in multiple modals */
.form-control:focus {
    border-radius: 6px;
    border: 1px solid #F0F0F0;
    background: #FFF;
    box-shadow: 0px 1.127px 2.253px 0px rgba(0, 0, 0, 0.05);
}

.custom_loc {
    margin-bottom: 1em;
}

label {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Google Places Autocomplete Z-index Fix */
.modal-open .pac-container {
    z-index: 9999999999 !important;
}

/* Slider Container - Used in filter modals */
.slider-container {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

/* Booking calendar */

.calender-box.calendar-duplicate .double-cal{width: 0;}
.calender-box.calendar-duplicate  .flatpickr-calendar{width: 100% !important;}

/* Notifications  */
.notification-bg .card-header.profile-img img {  margin-block: 1em; height: 60px;  width: 60px;  border-radius: 50%;  object-fit: unset !important;}

.day-row{  height: 35px;}
.service-availibility-calendar input[type="checkbox"]:checked::after, .add-service   input[type="checkbox"]:checked::after {content: unset;}
.service-availibility-calendar input[type="checkbox"]{   margin-bottom: 5px; border-radius: 3px;}

/* Dropdowns */

.dropdown.search_box.select-box .dropdown-menu { top: auto !important;  left: auto !important;  transform: none !important;  margin: 0 !important;  min-width: 180px;}
.dropdown.search_box.select-box,.dropdown.search_box.select-box * {  overflow: visible !important;}

/* [class*="table"], [class*="container"], [class*="row"], [class*="col"] */

/* [class*="table"]{  overflow-x: scroll ;} */
.toast {  z-index: 99999999999 !important;}
.mandatory-fields{color: red;}
.end-footer-sec li a {  text-decoration: unset ;   color: white ;}
.frst-footer-sec li a { color: #020C87 ;}
.add-service-thumbnail .image-input [data-kt-image-input-action=remove]{   left: 10em !important;   top: 10em !important;}
.form-control.is-invalid, .was-validated .form-control:invalid { background-image: unset }
.scrolltop.web i {  margin-left: 11px;  margin-top: 11px;}

/* .select2-container .select2-selection--single { height: 51px;} */
body .select2-container .select2-selection--single { height: auto;}

/* Swal Container */

.swal2-container.swal2-center.swal2-backdrop-show{z-index: 9999999999;}
body .review-section ul {font-size: 15px; font-weight: 600;}
.review-section .reviews {margin-block: 2.5em;}
.review-section ul li i { font-size: 18px;}
.review-section label{width: 100%; font-size: 14px; font-weight: 500;}
.review-section textarea {border-radius: 10px;border: 1px solid #DCDDE8; padding: 9px 12px 9px 16px; width: 100%; background: #FFF;}
.review-section textarea::placeholder{color: gray;}

.service-details-modal .business-data input[type="text"], .service-details-modal .business-data input[type="email"] { margin-bottom: 1em;}
.notification-filter .select2-container .select2-selection--single { height: 42px;}

#edit-product-certifications-form label.custom-checkbox { border: 1px solid var(--dark-cool-gray); border-radius: 20px; padding: 2px 12px; text-align: center; }
.profile-setting .card-body{ justify-content: center; display: flex; flex-direction: column; }
.profile-setting .card-body p{ margin-bottom: unset; }
.profile-gallery img { height: 200px; width: 200px; object-fit: cover; border-radius: 10px;}

.detail-item p {
    font-size: 14px;
}

.view-cat-image img, .view-sub-category img {
    height: 160px;
    width: 210px;
    object-fit: cover;
}

.view-sub-cateogry .detail-item, .view-category .detail-item {
    margin-bottom: 1em;
}

/* Featured Professional Ribbon */

body div:where(.swal2-container) button:where(.swal2-styled).swal2-confirm {background: var(--deep-blue);}

.featured-ribbon { position: relative;}
.featured-ribbon::before { content: "FEATURED" !important;  position: absolute; top: 0px; right: -1px; background: var(--deep-blue); color: #FFF !important;  padding: 2px 10px; background-repeat: no-repeat; font-size: 10px; font-weight: bold; letter-spacing: 0.5px; transform-origin: center; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);  border-radius: 2px; min-width: 70px;  text-align: center;}

.top-cards-height .card-body {  max-height: 138.75px}

.meet-the-team-swiper {position: unset}
.meet-the-team-swiper .swiper-button-next:after, .meet-the-team-swiper .swiper-button-prev:after,
.cert-swiper .swiper-button-prev:after, .cert-swiper .swiper-button-next:after {color: #FFF;}

.meet-the-team-swiper .swiper-button-prev:after, .meet-the-team-swiper .swiper-button-next:after,
.cert-swiper .swiper-button-prev:after, .cert-swiper .swiper-button-next:after {background-color: #020C87; padding: 10px 14px; border-radius: 50%;}

/* .meet-the-team-swiper .swiper-button-prev:after , .meet-the-team-swiper .swiper-button-next {  padding: 10px 14px;} */
.meet-the-team-swiper .swiper-button-prev, .cert-swiper .swiper-button-prev { background: unset; left: var(--swiper-navigation-sides-offset, -6px);  right: auto;  top: 14em;}
.meet-the-team-swiper .swiper-button-next, .cert-swiper .swiper-button-next { background: unset; right: var(--swiper-navigation-sides-offset, -9px); left: auto;  top: 14em;}

body .create-staff .iti__dropdown-content { width: 287px !important; padding: 4px 0px; }
.create-staff .iti--inline-dropdown .iti__country-list {  max-height: 185px;  width: 100%;}
.add-time-slot-container i { color: #FFF;}
.admin_cms input { border-radius: 10px;  border: 1px solid #DCDDE8;  padding: 9px 12px 9px 16px;  width: 100%;  background: #FFF;}
body .form-inputs-field:focus, body select.form-select-field:focus{  border-color: unset;  box-shadow: unset;}

/* .guarantee-section p.card-description, .guarantee-section p.card-heading {    display: -webkit-box; -webkit-box-orient: vertical; overflow: hidden;}
.guarantee-section p.card-heading {-webkit-line-clamp: 1;}
.guarantee-section p.card-description{ -webkit-line-clamp: 3;} */

.card-box h6{font-size: 15px;}
.admin-profile-img .cancel-avatar {right: -60px; top: 99px; border: 1px solid #4A4A4A; border-radius: 50%; height: 20px; width: 20px;  padding: 3px 4px; background: #FFF;  box-shadow: 0 .5rem 7.5rem .5rem rgba(10, 16, 17, -0.925);}
/* .refund_request_status .request-status::after { top: 55px; } */

.admin_cms .cms-page { display: flex;  justify-content: flex-end;}
.admin_cms .cms-page .blue-btn{  position: fixed;  bottom: 2em;   height: 55px;}

.customer-profile-img img {width: 80px; height: 80px; border-radius: 10px;}

.vat-delete i {  color: mediumvioletred;}

body .category-cuopon .select2-container--bootstrap5 .select2-selection--multiple:not(.form-select-sm):not(.form-select-lg)  {
    padding-top: 13px;
}

@media only screen and (min-width: 992px) {
    .mega_menu .dropdown-menu {  width: 100%;}
    .dropdown.mega_menu:hover .dropdown-menu {display: flex;}
    .mega_menu .dropdown-menu.show {display: flex; }
}

@media screen and (min-width: 769px) {
    #responsiveTable.manage-holiday tbody {display: grid;grid-template-columns: 1fr 1fr;gap: 10px;}
    #responsiveTable.manage-holiday tr {display: block;margin: 0;box-shadow: none;}
    #responsiveTable.manage-holiday td {width: 100%;}
    #responsiveTable.manage-holiday tr td:first-child {border-top-left-radius: 8px;border-bottom-left-radius: 8px;border-top-right-radius: 0;border-bottom-right-radius: 0;}
    /* #responsiveTable.manage-holiday.vat-managment-table tr td:first-child{width: 208px;} */
    #responsiveTable.manage-holiday tr td:last-child {border-top-left-radius: 0;border-bottom-left-radius: 0;border-top-right-radius: 8px;border-bottom-right-radius: 8px;}
    #responsiveTable.manage-holiday tbody .purple-btn{display: block; width: max-content;}
}



