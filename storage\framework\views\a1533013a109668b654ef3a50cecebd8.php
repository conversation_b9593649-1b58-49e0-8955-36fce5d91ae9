<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard ">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row px-3">
                <div class="col-md-12 d-flex justify-content-between">
                    <div>
                        <h6 class="semi_bold sora black">Analytics</h6>
                        <p class="fs-14 normal sora light-black">Get detailed insights into your salon's growth, customer preferences, and booking patterns.</p>
                    </div>
                    <!-- category -->
                    <div class="search_box select-box">
                        <select class="search_input" id="analytics-period-dropdown" onchange="changePeriod()">
                            <option value="weekly" <?php echo e($selectedPeriod == 'weekly' ? 'selected' : ''); ?>>Weekly</option>
                            <option value="monthly" <?php echo e($selectedPeriod == 'monthly' ? 'selected' : ''); ?>>Monthly</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="row row-gap-5 mb-10 card-wrapper px-3">
                <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                    <a href="#">
                        <div class=" card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-light-blue ">
                                    <?php echo $__env->make('svg.dollar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                            <div class="card-body w-150px">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Total Sales
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="<?php echo e($totalSales ?? 0); ?>" data-kt-countup-prefix="$">
                                </p>
                            </div>
                            
                        </div>
                    </a>
                </div>
                <div class="col-xl-3 col-lg-6 col-sm-6 col-12 ">
                    <a href="#">
                        <div class=" card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-light-green ">
                                    <?php echo $__env->make('svg.sales', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                            <div class="card-body w-150px">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    From last <?php echo e($selectedPeriod == 'monthly' ? 'month' : 'week'); ?>

                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="<?php echo e($fromLastPeriod ?? 0); ?>" data-kt-countup-prefix="$">
                                </p>
                            </div>
                            
                        </div>
                    </a>
                </div>
                <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                    <a href="#">
                        <div class=" card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-purple">
                                    <?php echo $__env->make('svg.earning', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                            <div class="card-body w-150px ">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Completed Bookings
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="<?php echo e($completedBookings ?? 0); ?>">
                                </p>
                            </div>
                            
                        </div>
                    </a>
                </div>
                <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                    <a href="#">
                        <div class=" card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-orange">
                                    <?php echo $__env->make('svg.booking', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                            <div class="card-body w-150px">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Total Bookings
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="<?php echo e($totalBookings ?? 0); ?>">
                                </p>
                            </div>
                            
                        </div>
                    </a>
                </div>
            </div>

            <!-- Daily Revenue Breakdown Section -->
            <div class="row mb-4 ps-3">
                <div class="col-md-12">
                    <h6 class="semi_bold sora black">Daily Revenue Breakdown</h6>
                </div>
            </div>

            <div class="row row-gap-5 mb-10 px-3">
                <!-- Total Daily Revenue Card -->
                <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                    <a href="#">
                        <div class="card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-light-blue">
                                    <?php echo $__env->make('svg.dollar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                            <div class="card-body w-150px">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Total Daily Revenue
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="<?php echo e($dailyRevenueData['dailyRevenue'] ?? 0); ?>"
                                    data-kt-countup-prefix="$">
                                </p>
                            </div>
                            
                        </div>
                    </a>
                </div>

                <!-- Revenue by Service/Product Card -->
                <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                    <a href="#">
                        <div class="card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-light-green">
                                    <?php echo $__env->make('svg.sales', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                            <div class="card-body w-150px">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Revenue by service/product
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="<?php echo e($dailyRevenueData['serviceRevenue'] ?? 0); ?>"
                                    data-kt-countup-prefix="$">
                                </p>
                            </div>
                            
                        </div>
                    </a>
                </div>

                <!-- Net Revenue Card -->
                <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                    <a href="#">
                        <div class="card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-purple">
                                    <?php echo $__env->make('svg.earning', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                            <div class="card-body w-150px">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Net revenue
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="<?php echo e($dailyRevenueData['netRevenue'] ?? 0); ?>"
                                    data-kt-countup-prefix="$">
                                </p>
                            </div>
                            
                        </div>
                    </a>
                </div>

                <!-- Discounts Given Card -->
                <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                    <a href="#">
                        <div class="card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-orange">
                                    <?php echo $__env->make('svg.sales', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                            <div class="card-body w-150px">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Discounts given
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="<?php echo e($dailyRevenueData['discountsGiven'] ?? 0); ?>"
                                    data-kt-countup-prefix="$">
                                </p>
                            </div>
                            
                        </div>
                    </a>
                </div>
            </div>

            <!-- Service Performance Section -->
            <div class="row mb-4 px-3">
                <div class="col-md-12">
                    <h6 class="semi_bold sora black">Service Performance</h6>
                </div>
            </div>

            <div class="row mb-10 px-3">
                <div class="col-md-12">
                    <div class="card-box">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr class="border-bottom">
                                        <th class="fs-14 semi_bold sora black text-start">Service Name</th>
                                        <th class="fs-14 semi_bold sora black text-center">Bookings</th>
                                        <th class="fs-14 semi_bold sora black text-center">Revenue</th>
                                        <th class="fs-14 semi_bold sora black text-center">Duration</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if(isset($servicePerformance) && count($servicePerformance) > 0): ?>
                                        <?php $__currentLoopData = $servicePerformance; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td class="fs-14 normal sora black text-start">
                                                    <div class="d-flex align-items-center">
                                                        <div class="service-icon me-3">
                                                            <div class="icon_cards bg-light-blue">
                                                                <?php echo $__env->make('svg.booking', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <p class="fs-14 semi_bold sora black m-0">
                                                                <?php echo e($service['name'] ?? 'N/A'); ?></p>
                                                            <p class="fs-12 normal light-black opacity-6 m-0">
                                                                <?php echo e($service['category']['name'] ?? ''); ?></p>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="fs-14 normal sora black text-center">
                                                    <span class="fs-16 semi_bold"><?php echo e($service['bookings'] ?? 0); ?></span>
                                                    
                                                </td>
                                                <td class="fs-14 normal sora black text-center">
                                                    <span
                                                        class="fs-16 semi_bold">$<?php echo e(number_format($service['revenue'] ?? 0, 2)); ?></span>
                                                    
                                                </td>
                                                <td class="fs-14 normal sora black text-center">
                                                    <span class="fs-16 semi_bold"><?php echo e($service['duration'] ?? 0); ?>

                                                        min</span>
                                                    
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="6" class="text-center fs-14 normal light-black py-4">
                                                No service performance data available
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <?php if(auth()->user()->hasRole('business') && auth()->user()->staffs()->count() > 0): ?>
                <!-- Staff Performance Section -->
                <div class="row mb-4 px-3">
                    <div class="col-md-12">
                        <h6 class="semi_bold sora black">Staff Performance</h6>
                    </div>
                </div>

                <div class="row mb-10 px-3">
                    <div class="col-md-12">
                        <div class="card-box">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr class="border-bottom">
                                            <th class="fs-14 semi_bold sora black text-start">Staff Name</th>
                                            <th class="fs-14 semi_bold sora black text-center">Revenue</th>
                                            <th class="fs-14 semi_bold sora black text-center">Services Performed</th>
                                            <th class="fs-14 semi_bold sora black text-center">Client Feedback</th>
                                            <th class="fs-14 semi_bold sora black text-center">Utilization Rate</th>
                                            <th class="fs-14 semi_bold sora black text-center">Hours Worked</th>
                                            <th class="fs-14 semi_bold sora black text-center">Service/Retail Revenue</th>
                                            <th class="fs-14 semi_bold sora black text-center">Total Pay</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if(isset($staffPerformance) && count($staffPerformance) > 0): ?>
                                            <?php $__currentLoopData = $staffPerformance; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $staff): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td class="fs-14 normal sora black text-start">
                                                        <div class="d-flex align-items-center">
                                                            <div class="staff-avatar me-3">
                                                                <img src="<?php echo e($staff['avatar'] ?? asset('website/assets/images/default.png')); ?>"
                                                                    class="rounded-circle" width="40" height="40"
                                                                    alt="Staff Avatar">
                                                            </div>
                                                            <div>
                                                                <p class="fs-14 semi_bold sora black m-0">
                                                                    <?php echo e($staff['name'] ?? 'N/A'); ?></p>
                                                                <p class="fs-12 normal light-black opacity-6 m-0">
                                                                    <?php echo e($staff['role'] ?? ''); ?></p>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="fs-14 normal sora black text-center">
                                                        <span
                                                            class="fs-16 semi_bold">$<?php echo e(number_format($staff['revenue'] ?? 0, 2)); ?></span>
                                                        
                                                    </td>
                                                    <td class="fs-14 normal sora black text-center">
                                                        <span
                                                            class="fs-16 semi_bold"><?php echo e($staff['services_performed'] ?? 0); ?></span>
                                                        
                                                    </td>
                                                    <td class="fs-14 normal sora black text-center">
                                                        <div class="d-flex align-items-center justify-content-center">
                                                            <span
                                                                class="fs-16 semi_bold me-2"><?php echo e(number_format($staff['client_feedback'] ?? 0, 1)); ?></span>
                                                            
                                                        </div>
                                                        
                                                    </td>
                                                    <td class="fs-14 normal sora black text-center">
                                                        <span
                                                            class="fs-16 semi_bold"><?php echo e($staff['utilization_rate'] ?? 0); ?>%</span>
                                                        
                                                    </td>
                                                    <td class="fs-14 normal sora black text-center">
                                                        <span
                                                            class="fs-16 semi_bold"><?php echo e($staff['hours_worked'] ?? 0); ?>h</span>
                                                        
                                                    </td>
                                                    <td class="fs-14 normal sora black text-center">
                                                        <span
                                                            class="fs-16 semi_bold">$<?php echo e(number_format($staff['service_retail_revenue'] ?? 0, 2)); ?></span>
                                                        
                                                    </td>
                                                    <td class="fs-14 normal sora black text-center">
                                                        <span
                                                            class="fs-16 semi_bold">$<?php echo e(number_format($staff['total_pay'] ?? 0, 2)); ?></span>
                                                        
                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="8" class="text-center fs-14 normal light-black py-4">
                                                    No staff performance data available
                                                </td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Platform Financial Overview Section -->
                <div class="row mb-4 px-3">
                    <div class="col-md-12">
                        <h6 class="semi_bold sora black">Platform Financial Overview</h6>
                    </div>
                </div>


            <div class="row row-gap-5 mb-10 px-3">
                <!-- Total platform revenue Card -->
                <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                    <a href="#">
                        <div class="card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-light-blue">
                                    <?php echo $__env->make('svg.dollar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                            <div class="card-body w-150px">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Total platform revenue
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="<?php echo e($totalPlatformRevenue ?? 0); ?>" data-kt-countup-prefix="$">
                                </p>
                            </div>
                            <div class="card-footer w-50">
                                <div
                                    class="fs-12 w-700 <?php echo e(($platformRevenueChange ?? 0) >= 0 ? 'green green-box' : 'red red-box'); ?>">
                                    <i
                                        class="fa-solid fa-arrow-<?php echo e(($platformRevenueChange ?? 0) >= 0 ? 'up' : 'down'); ?> analytics-green-arrow"></i>
                                    <?php echo e(abs($platformRevenueChange ?? 0)); ?>%
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
                <!-- Platform commission/fees earned Card -->
                <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                    <a href="#">
                        <div class="card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-light-green">
                                    <?php echo $__env->make('svg.earning', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                            <div class="card-body w-150px">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Platform commission/fees earned
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="<?php echo e($platformCommission ?? 0); ?>" data-kt-countup-prefix="$">
                                </p>
                            </div>
                            <div class="card-footer w-50">
                                <div
                                    class="fs-12 w-700 <?php echo e(($commissionChange ?? 0) >= 0 ? 'green green-box' : 'red red-box'); ?>">
                                    <i
                                        class="fa-solid fa-arrow-<?php echo e(($commissionChange ?? 0) >= 0 ? 'up' : 'down'); ?> analytics-green-arrow"></i>
                                    <?php echo e(abs($commissionChange ?? 0)); ?>%
                                </div>
                            </div>
                        </div>
                    </a>
                </div>

                <!-- Total payouts to users Card -->
                <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                    <a href="#">
                        <div class="card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-purple">
                                    <?php echo $__env->make('svg.sales', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                            <div class="card-body w-150px">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Total payouts to users
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="<?php echo e($totalPayouts ?? 0); ?>" data-kt-countup-prefix="$">
                                </p>
                            </div>
                            <div class="card-footer w-50">
                                <div
                                    class="fs-12 w-700 <?php echo e(($payoutsChange ?? 0) >= 0 ? 'green green-box' : 'red red-box'); ?>">
                                    <i
                                        class="fa-solid fa-arrow-<?php echo e(($payoutsChange ?? 0) >= 0 ? 'up' : 'down'); ?> analytics-green-arrow"></i>
                                    <?php echo e(abs($payoutsChange ?? 0)); ?>%
                                </div>
                            </div>
                        </div>
                    </a>
                </div>

                <!-- Outstanding balances Card -->
                <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                    <a href="#">
                        <div class="card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-orange">
                                    <?php echo $__env->make('svg.booking', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                            <div class="card-body w-150px">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Outstanding balances
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="<?php echo e($outstandingBalances ?? 0); ?>" data-kt-countup-prefix="$">
                                </p>
                            </div>
                            <div class="card-footer w-50">
                                <div
                                    class="fs-12 w-700 <?php echo e(($balancesChange ?? 0) >= 0 ? 'green green-box' : 'red red-box'); ?>">
                                    <i
                                        class="fa-solid fa-arrow-<?php echo e(($balancesChange ?? 0) >= 0 ? 'up' : 'down'); ?> analytics-green-arrow"></i>
                                    <?php echo e(abs($balancesChange ?? 0)); ?>%
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
                <?php endif; ?>
            <?php if(auth()->user()->hasAnyRole(['admin', 'super admin'])): ?>
                <!-- User Acquisition & Churn Section -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h6 class="semi_bold sora black">User Acquisition & Churn</h6>
                    </div>
                </div>

                <div class="row row-gap-5 mb-10">
                    <!-- New user signups Card -->
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="#">
                            <div class="card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-light-blue">
                                        <i class="fas fa-user-plus text-white"></i>
                                    </div>
                                </div>
                                <div class="card-body w-150px">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        New user signups
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                        data-kt-countup-value="<?php echo e($userAcquisition['new_users'] ?? 0); ?>">
                                    </p>
                                </div>
                                <div class="card-footer w-50">
                                    <div
                                        class="fs-12 w-700 <?php echo e(($userAcquisition['new_users_change'] ?? 0) >= 0 ? 'green green-box' : 'red red-box'); ?>">
                                        <i
                                            class="fa-solid fa-arrow-<?php echo e(($userAcquisition['new_users_change'] ?? 0) >= 0 ? 'up' : 'down'); ?> analytics-green-arrow"></i>
                                        <?php echo e(abs($userAcquisition['new_users_change'] ?? 0)); ?>%
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Active/inactive users Card -->
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="#">
                            <div class="card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-light-green">
                                        <i class="fas fa-users text-white"></i>
                                    </div>
                                </div>
                                <div class="card-body w-150px">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        Active/inactive users
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0">
                                        <span
                                            class="text-success"><?php echo e($userAcquisition['active_users'] ?? 0); ?></span>/<span
                                            class="text-danger"><?php echo e(($userAcquisition['new_users'] ?? 0) - ($userAcquisition['active_users'] ?? 0)); ?></span>
                                    </p>
                                </div>
                                <div class="card-footer w-50">
                                    <div
                                        class="fs-12 w-700 <?php echo e(($userAcquisition['active_users_change'] ?? 0) >= 0 ? 'green green-box' : 'red red-box'); ?>">
                                        <i
                                            class="fa-solid fa-arrow-<?php echo e(($userAcquisition['active_users_change'] ?? 0) >= 0 ? 'up' : 'down'); ?> analytics-green-arrow"></i>
                                        <?php echo e(abs($userAcquisition['active_users_change'] ?? 0)); ?>%
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Churn rate Card -->
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="#">
                            <div class="card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-purple">
                                        <i class="fas fa-user-minus text-white"></i>
                                    </div>
                                </div>
                                <div class="card-body w-150px">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        Churn rate
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                        data-kt-countup-value="<?php echo e($userAcquisition['churn_rate'] ?? 0); ?>"
                                        data-kt-countup-suffix="%">
                                    </p>
                                </div>
                                <div class="card-footer w-50">
                                    <div
                                        class="fs-12 w-700 <?php echo e(($userAcquisition['churn_rate_change'] ?? 0) <= 0 ? 'green green-box' : 'red red-box'); ?>">
                                        <i
                                            class="fa-solid fa-arrow-<?php echo e(($userAcquisition['churn_rate_change'] ?? 0) <= 0 ? 'down' : 'up'); ?> analytics-green-arrow"></i>
                                        <?php echo e(abs($userAcquisition['churn_rate_change'] ?? 0)); ?>%
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Net user growth Card -->
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="#">
                            <div class="card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-orange">
                                        <i class="fas fa-chart-line text-white"></i>
                                    </div>
                                </div>
                                <div class="card-body w-150px">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        Net user growth
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                        data-kt-countup-value="<?php echo e(($userAcquisition['new_users'] ?? 0) - (($userAcquisition['churn_rate'] ?? 0) * ($userAcquisition['active_users'] ?? 0)) / 100); ?>">
                                    </p>
                                </div>
                                <div class="card-footer w-50">
                                    <div
                                        class="fs-12 w-700 <?php echo e(($userAcquisition['retention_rate_change'] ?? 0) >= 0 ? 'green green-box' : 'red red-box'); ?>">
                                        <i
                                            class="fa-solid fa-arrow-<?php echo e(($userAcquisition['retention_rate_change'] ?? 0) >= 0 ? 'up' : 'down'); ?> analytics-green-arrow"></i>
                                        <?php echo e(abs($userAcquisition['retention_rate_change'] ?? 0)); ?>%
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>

                <!-- Subscription & Plan Analytics Section -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h6 class="semi_bold sora black">Subscription & Plan Analytics</h6>
                    </div>
                </div>

                <div class="row row-gap-5 mb-10">
                    <!-- Users per plan Card -->
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="#">
                            <div class="card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-light-blue">
                                        <i class="fas fa-user-tag text-white"></i>
                                    </div>
                                </div>
                                <div class="card-body w-150px">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        Users per plan
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                        data-kt-countup-value="<?php echo e($subscriptionAnalytics['activeSubscriptions'] ?? 0); ?>">
                                    </p>
                                </div>
                                <div class="card-footer w-50">
                                    <div
                                        class="fs-12 w-700 <?php echo e(($subscriptionAnalytics['subscriptionChange'] ?? 0) >= 0 ? 'green green-box' : 'red red-box'); ?>">
                                        <i
                                            class="fa-solid fa-arrow-<?php echo e(($subscriptionAnalytics['subscriptionChange'] ?? 0) >= 0 ? 'up' : 'down'); ?> analytics-green-arrow"></i>
                                        <?php echo e(abs($subscriptionAnalytics['subscriptionChange'] ?? 0)); ?>%
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Revenue per plan Card -->
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="#">
                            <div class="card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-light-green">
                                        <?php echo $__env->make('svg.dollar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                    </div>
                                </div>
                                <div class="card-body w-150px">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        Revenue per plan
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                        data-kt-countup-value="<?php echo e($subscriptionAnalytics['monthlyRecurringRevenue'] ?? 0); ?>"
                                        data-kt-countup-prefix="$">
                                    </p>
                                </div>
                                <div class="card-footer w-50">
                                    <div
                                        class="fs-12 w-700 <?php echo e(($subscriptionAnalytics['mrrChange'] ?? 0) >= 0 ? 'green green-box' : 'red red-box'); ?>">
                                        <i
                                            class="fa-solid fa-arrow-<?php echo e(($subscriptionAnalytics['mrrChange'] ?? 0) >= 0 ? 'up' : 'down'); ?> analytics-green-arrow"></i>
                                        <?php echo e(abs($subscriptionAnalytics['mrrChange'] ?? 0)); ?>%
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Upgrades/downgrades Card -->
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="#">
                            <div class="card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-purple">
                                        <i class="fas fa-exchange-alt text-white"></i>
                                    </div>
                                </div>
                                <div class="card-body w-150px">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        Upgrades/downgrades
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0">
                                        <span
                                            class="text-success"><?php echo e($subscriptionAnalytics['planUpgrades'] ?? 0); ?></span>/<span
                                            class="text-warning">0</span>
                                    </p>
                                </div>
                                <div class="card-footer w-50">
                                    <div
                                        class="fs-12 w-700 <?php echo e(($subscriptionAnalytics['upgradeChange'] ?? 0) >= 0 ? 'green green-box' : 'red red-box'); ?>">
                                        <i
                                            class="fa-solid fa-arrow-<?php echo e(($subscriptionAnalytics['upgradeChange'] ?? 0) >= 0 ? 'up' : 'down'); ?> analytics-green-arrow"></i>
                                        <?php echo e(abs($subscriptionAnalytics['upgradeChange'] ?? 0)); ?>%
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- Cancellations per plan Card -->
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="#">
                            <div class="card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-orange">
                                        <i class="fas fa-times-circle text-white"></i>
                                    </div>
                                </div>
                                <div class="card-body w-150px">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        Cancellations per plan
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                        data-kt-countup-value="<?php echo e(round((($subscriptionAnalytics['churnRate'] ?? 0) * ($subscriptionAnalytics['activeSubscriptions'] ?? 0)) / 100)); ?>">
                                    </p>
                                </div>
                                <div class="card-footer w-50">
                                    <div
                                        class="fs-12 w-700 <?php echo e(($subscriptionAnalytics['churnChange'] ?? 0) <= 0 ? 'green green-box' : 'red red-box'); ?>">
                                        <i
                                            class="fa-solid fa-arrow-<?php echo e(($subscriptionAnalytics['churnChange'] ?? 0) <= 0 ? 'down' : 'up'); ?> analytics-green-arrow"></i>
                                        <?php echo e(abs($subscriptionAnalytics['churnChange'] ?? 0)); ?>%
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            <?php endif; ?>

            <div class="row px-3">
                <div class="col-lg-8">
                    <div class="  card-box">
                        <p class="black sora semi_bold">Peak Booking Time</p>
                        <canvas id="popularServicesChart"></canvas>
                    </div>
                </div>
                <?php if(($upcomingBookings ?? 0) > 0 || ($completedBookings ?? 0) > 0): ?>
                <div class="col-lg-4">
                    <div class="  card-box">
                        <p class="black sora semi_bold">Upcoming & Completed Bookings</p>
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="chart-container-booking">
                                <canvas id="bookingChart" style="width:300px ; height: 400px;  "></canvas>
                            </div>
                            <div id="chartLegend" style="margin-left: 30px;"></div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script>
        // Function to change period and reload page
        function changePeriod() {
            const period = document.getElementById('analytics-period-dropdown').value;
            window.location.href = '<?php echo e(route('business_analytics')); ?>?period=' + period;
        }

        // Peak Booking Time Chart
        const ctx = document.getElementById('popularServicesChart').getContext('2d');
        const peakBookingData = {
            labels: <?php echo \Illuminate\Support\Js::from($peakBookingLabels ?? [])->toHtml() ?>,
            datasets: [{
                label: 'Peak Booking Times',
                data: <?php echo \Illuminate\Support\Js::from($peakBookingData ?? [])->toHtml() ?>,
                borderColor: '#3B82F6',
                backgroundColor: function(context) {
                    const chart = context.chart;
                    const {
                        ctx,
                        chartArea
                    } = chart;
                    if (!chartArea) return null;

                    const gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);
                    gradient.addColorStop(0, 'rgba(59, 130, 246, 0.00)');
                    gradient.addColorStop(1, 'rgba(59, 130, 246, 0.15)');
                    return gradient;
                },
                fill: true,
                tension: 0.5,
                pointBackgroundColor: '#3B82F6',
                pointBorderColor: 'transparent',
                pointRadius: 4,
                pointHoverRadius: 7,
            }]
        };

        const peakBookingConfig = {
            type: 'line',
            data: peakBookingData,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: true,
                        position: 'bottom',
                        align: 'start',
                        labels: {
                            usePointStyle: true,
                            pointStyle: 'circle',
                            padding: 20,
                            boxWidth: 5,
                            boxHeight: 5,
                        }
                    },
                    tooltip: {
                        enabled: true,
                        callbacks: {
                            label: function(context) {
                                return context.raw + ' Bookings';
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 12,
                                weight: '400',
                                opacity: 0.6,
                            },
                            color: '#363636'
                        }
                    },
                    y: {
                        grid: {
                            display: true
                        },
                        beginAtZero: true,
                        ticks: {
                            font: {
                                size: 12,
                                weight: 400,
                            },
                            color: '#363636',
                            callback: function(value) {
                                return Math.floor(value) === value ? value : '';
                            }
                        }
                    }
                }
            }
        };

        const popularServicesChart = new Chart(ctx, peakBookingConfig);

        // Upcoming & Completed Bookings Donut Chart
        <?php if(($upcomingBookings ?? 0) > 0 || ($completedBookings ?? 0) > 0): ?>
        const bookingCtx = document.getElementById('bookingChart');
        if (bookingCtx) {
            const upcomingBookings = <?php echo e($upcomingBookings ?? 0); ?>;
            const completedBookings = <?php echo e($completedBookings ?? 0); ?>;

            const bookingChartData = {
                labels: ['Upcoming Bookings', 'Completed Bookings'],
                datasets: [{
                    label: 'Booking Analytics',
                    data: [upcomingBookings, completedBookings],
                    backgroundColor: ['#3B82F6', '#10B981'],
                    borderColor: '#fff',
                    borderWidth: 3,
                    hoverOffset: 4,
                }]
            };

            const bookingChartConfig = {
                type: 'doughnut',
                data: bookingChartData,
                options: {
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                title: (context) => context[0].label,
                                label: (context) => `${context.raw} Bookings`
                            }
                        }
                    },
                    cutout: '75%',
                    responsive: true,
                    maintainAspectRatio: false
                }
            };

            const bookingChart = new Chart(bookingCtx, bookingChartConfig);

            // Create custom legend
            const legendContainer = document.getElementById('chartLegend');
            if (legendContainer) {
                legendContainer.innerHTML = `
                    <div style="display: flex; flex-direction: column; gap: 15px;">
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <div style="width: 12px; height: 12px; background-color: #3B82F6; border-radius: 50%;"></div>
                            <span style="font-size: 14px; color: #363636;">Upcoming (${upcomingBookings})</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <div style="width: 12px; height: 12px; background-color: #10B981; border-radius: 50%;"></div>
                            <span style="font-size: 14px; color: #363636;">Completed (${completedBookings})</span>
                        </div>
                    </div>
                `;
            }
        }
        <?php endif; ?>
    </script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('css'); ?>
    <style>
        .red-box {
            background-color: #fee2e2;
            color: #dc2626;
            padding: 4px 8px;
            border-radius: 4px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .green-box {
            background-color: #dcfce7;
            color: #059669;
            padding: 4px 8px;
            border-radius: 4px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .analytics-green-arrow {
            font-size: 10px;
        }

        #analytics-period-dropdown {
            border: 1px solid #e1e5e9;
            border-radius: 6px;
            padding: 8px 12px;
            background: white;
            font-size: 14px;
            color: #5e6278;
            min-width: 120px;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('dashboard.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\git-file\anders\resources\views/dashboard/business/analytics.blade.php ENDPATH**/ ?>