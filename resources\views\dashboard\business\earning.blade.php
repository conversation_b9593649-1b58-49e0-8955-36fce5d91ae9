@extends('dashboard.layout.master')

@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid booking">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row">
                <div class="col-md-12 ps-md-5 ps-sm-5 ps-5">
                    <h6 class="semi_bold sora black">Earnings</h6>
                    <p class="fs-14 normal sora light-black">Quality services at honest prices - because you deserve the best without 
                        breaking the bank </p>
                </div>
            </div>
            <div class="row row-gap-5 mb-10 card-wrapper ps-md-3 ps-sm-3 ps-3">
                <div class="col-xl-4 col-lg-4 col-sm-6 col-12">
                    <a href="javascript:void(0)">
                        <div class=" card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-deep-blue">
                                    @include('svg.dollar')
                                </div>
                            </div>
                            <div class="card-body w-50">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Total Revenue
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="{{ $earningsStats['total_revenue'] ?? 0 }}" data-kt-countup-prefix="$">
                                </p>
                            </div>
                            {{-- <div class="card-footer ">
                                <div class="fs-12 w-700 {{ $earningsStats['total_revenue_change'] >= 0 ? 'green-box green' : 'red-box red' }}">
                                    <i class="fa-solid fa-arrow-{{ $earningsStats['total_revenue_change'] >= 0 ? 'up analytics-green-arrow' : 'down analytics-red-arrow' }}"></i>
                                    {{ abs($earningsStats['total_revenue_change']) }}%
                                </div>
                            </div> --}}
                        </div>
                    </a>
                </div>
                <div class="col-xl-4 col-lg-4 col-sm-6 col-12">
                    <a href="javascript:void(0)">
                        <div class=" card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-light-blue">
                                    @include('svg.earning')
                                </div>
                            </div>
                            <div class="card-body w-50 ">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Pending Amount
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="{{ $earningsStats['pending_amount'] ?? 0 }}" data-kt-countup-prefix="$">
                                </p>
                            </div>
                            {{-- <div class="card-footer">
                                <div class="fs-12 w-700 {{ $earningsStats['pending_amount_change'] >= 0 ? 'green-box green' : 'red-box red' }}">
                                    <i class="fa-solid fa-arrow-{{ $earningsStats['pending_amount_change'] >= 0 ? 'up analytics-green-arrow' : 'down analytics-red-arrow' }}"></i>
                                    {{ abs($earningsStats['pending_amount_change']) }}%
                                </div>
                            </div> --}}
                        </div>
                    </a>
                </div>
                <div class="col-xl-4 col-lg-4 col-sm-6 col-12">
                    <a href="javascript:void(0)">
                        <div class=" card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-success">
                                    @include('svg.booking')
                                </div>
                            </div>
                            <div class="card-body w-50 ">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Completed Bookings
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="{{ $earningsStats['completed_bookings_count'] ?? 0 }}">
                                </p>
                            </div>
                            {{-- <div class="card-footer">
                                <div class="fs-12 w-700 {{ $earningsStats['completed_bookings_change'] >= 0 ? 'green-box green' : 'red-box red' }}">
                                    <i class="fa-solid fa-arrow-{{ $earningsStats['completed_bookings_change'] >= 0 ? 'up analytics-green-arrow' : 'down analytics-red-arrow' }}"></i>
                                    {{ abs($earningsStats['completed_bookings_change']) }}%
                                </div>
                            </div> --}}
                        </div>
                    </a>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-12">
                    <div class="table-container">
                        <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                            <div class="search_box">
                                <label for="customSearchInput">
                                    <i class="fas fa-search"></i>
                                </label>
                                <input class="search_input search" type="text" id="customSearchInput"
                                    placeholder="Search..." />
                            </div>
                            <!-- Status filter removed since we only show completed bookings -->

                            <!-- category -->
                            <div class="search_box select-box">
                                <select class="search_input" id="categoryFilter">
                                    <option value="Category">Category</option>
                                    <option value="Category">All</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->name }}">{{ $category->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <!-- Date Picker -->

                            <label for="datePicker" class="date_picker">
                                <div class="date-picker-container">
                                    <i class="bi bi-calendar-event calender-icon"></i>
                                    <input type="text" name="datePicker" class="datePicker w-200px ms-3">
                                    <i class="fa fa-chevron-down down-arrow ms-9"></i>

                                </div>
                            </label>
                            <div class="search_box d-block ms-auto">
                                <button type="button" id="exportEarningsBtn" class="search_input fs-14 normal link-gray ">
                                    Export <i class="bi bi-file-arrow-down ms-1 file-icon"></i>
                                </button>
                            </div>

                        </div>
                        <table id="responsiveTable" class="display nowrap w-100">
                            <thead>
                                <tr>
                                    <th>Customer Name</th>
                                    <th>Service Type</th>
                                    <th>Client Name</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody id="earningTableBody">
                                @include('dashboard.business.partials.earning-table-rows', ['bookings' => $bookings])
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
<script>
    $(document).ready(function() {
        let searchTimeout;
        let currentCategory = 'Category';

        // Search input with debouncing
        $('#customSearchInput').on('keyup', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                performSearch();
            }, 300);
        });

        // Category dropdown handling
        $('#categoryFilter').on('change', function() {
            currentCategory = $(this).val();
            performSearch();
        });

        function performSearch() {
            const searchQuery = $('#customSearchInput').val();

            // Show loading state
            $('#earningTableBody').html('<tr><td colspan="7" class="text-center">Loading...</td></tr>');

            $.ajax({
                url: '{{ route('earning.filter') }}',
                type: 'GET',
                data: {
                    search: searchQuery,
                    category: currentCategory
                },
                success: function(response) {
                    if (response.success) {
                        $('#earningTableBody').html(response.html);

                        // Show result count
                        if (searchQuery || currentCategory !== 'Category') {
                            console.log(`Found ${response.count} earning(s)`);
                        }
                    } else {
                        $('#earningTableBody').html(
                            '<tr><td colspan="7" class="text-center">Error loading earnings</td></tr>'
                            );
                    }
                },
                error: function() {
                    $('#earningTableBody').html(
                        '<tr><td colspan="7" class="text-center">Error loading earnings</td></tr>'
                        );
                }
            });
        }

        // Handle export button click
        $(document).on('click', '#exportEarningsBtn', function(e) {
            e.preventDefault();

            // Show loading state
            const originalText = $(this).html();
            $(this).html('<span class="spinner-border spinner-border-sm me-2" role="status"></span>Exporting...');

            // Build export URL with current filters
            const searchQuery = $('#customSearchInput').val();
            let exportUrl = '{{ route("earning.export") }}';
            let params = [];

            if (searchQuery) {
                params.push('search=' + encodeURIComponent(searchQuery));
            }
            if (currentCategory && currentCategory !== 'Category') {
                params.push('category=' + encodeURIComponent(currentCategory));
            }

            if (params.length > 0) {
                exportUrl += '?' + params.join('&');
            }

            // Trigger download with filters
            window.location.href = exportUrl;

            // Reset button after a delay
            setTimeout(function() {
                $('#exportEarningsBtn').html(originalText);
            }, 2000);
        });

        // Booking action handlers removed since we only show completed bookings
    });
</script>
@endpush
