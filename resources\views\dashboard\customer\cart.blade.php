@extends('website.layout.master')
@push('css')
<style>
    #couponError {
        font-size: 14px;
        margin-top: 5px;
    }
    #couponSuccess {
        font-size: 14px;
        margin-top: 5px;
    }
    .form-control.is-invalid {
        border-color: #dc3545;
    }
    .form-control.is-valid {
        border-color: #28a745;
    }
    #applyCouponBtn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    #removeCouponBtn {
        right: 10px;
        display: none !important;
    }

    #removeCouponBtn.show {
        display: inline-block !important;
    }

    #appliedCouponDisplay {
        border: 1px solid #28a745;
    }

    #coupon:read-only {
        background-color: #f8f9fa;
    }
</style>
@endpush

@section('content')
    <div class="app-content flex-column-fluid customer_dashboard addfamily cart-section padding-block position-relative">
        <div class="app-container container padding-block h-100">
            <div class="row align-items-center justify-content-center h-100">
                <div class="col-md-12">
                    <div class="container-box">
                        <!-- Step 1: Cart -->
                        {{-- <div class="step active" id="step1"> --}}
                        @include('dashboard.templates.cart-stepper.cart-step')
                        {{-- </div> --}}
                        <!-- Step 2: Payment -->
                        {{-- <div class="step" id="step2">
                            @include('dashboard.templates.cart-stepper.payment-step')
                        </div>
                        <!-- Step 3: Booking Confirmation -->
                        <div class="step" id="step3">
                            @include('dashboard.templates.cart-stepper.booking-confirmation')
                        </div> --}}
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script>
        // Handle cart item deletion
        $(document).on('click', '.delete-cart-item', function() {
            const serviceId = $(this).data('service-id');
            const bookingDate = $(this).data('booking-date');
            const bookingTime = $(this).data('booking-time');
            const cartItemId = $(this).data('cart-item-id');
            Swal.fire({
                title: 'Confirm Delete',
                text: 'Are you sure you want to remove this item from your cart?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Delete',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    deleteCartItem(serviceId, bookingDate, bookingTime, cartItemId, $(this));
                }
            });
        });
        // Function to delete cart item from session
        function deleteCartItem(serviceId, bookingDate, bookingTime, cartItemId, buttonElement) {
            $.ajax({
                url: "{{ route('remove_cart_item') }}",
                method: 'POST',
                data: {
                    service_id: serviceId,
                    booking_date: bookingDate,
                    booking_time: bookingTime,
                    cart_item_id: cartItemId,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.status) {
                        // Remove the cart item from the DOM
                        let selector = cartItemId
                            ? '.cart-item[data-cart-item-id="' + cartItemId + '"]'
                            : '.cart-item[data-service-id="' + serviceId + '"][data-booking-date="' + bookingDate + '"][data-booking-time="' + bookingTime + '"]';
                        const $target = $(selector).length ? $(selector) : buttonElement.closest('.cart-item');
                        $target.fadeOut(300, function() {
                            $(this).remove();
                            updateCartCount();
                            // Show success message
                            Swal.fire({
                                icon: 'success',
                                title: 'Success',
                                text: 'Item removed from cart successfully'
                            }).then(function(){
                                // If last item removed, reload the page to reset UI state
                                if ($('.cart-item').length === 0) {
                                    window.location.reload();
                                }
                            });
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: response.message || 'Failed to remove item from cart'
                        });
                    }
                },
                error: function() {
                    showToast('error', 'An error occurred while removing the item');
                }
            });
        }

        // Function to update cart count
        function updateCartCount() {
            const cartCount = $('.cart-item').length;
            $('.cart-count').text(cartCount);

            // Update any cart badges or counters
            if (cartCount === 0) {
                $('.cart-badge').hide();
            }
        }

        // Coupon validation functionality
        $(document).on('click', '#applyCouponBtn', function() {
            const couponCode = $('#coupon').val().trim();
            if (!couponCode) {
                showCouponError('Please enter a coupon code');
                return;
            }
            // Show loading state
            const btn = $(this);
            const originalText = btn.text();
            btn.text('Applying...').prop('disabled', true);

            // Clear previous messages
            hideCouponMessages();

            // AJAX request to validate coupon
            $.ajax({
                url: "{{ route('apply_coupon') }}",
                method: 'POST',
                data: {
                    coupon_code: couponCode,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    console.log('Coupon response:', response); // Debug log
                    if (response.status) {
                        showCouponSuccess(response.message || 'Coupon applied successfully!');

                        // Store coupon info globally for payment link
                        window.appliedCoupon = {
                            code: $('#coupon').val().trim(),
                            discount_amount: response.discount_amount,
                            original_total: response.original_total
                        };

                        // Show applied state with actual discount amount
                        showAppliedCouponState(response.discount_amount);

                        // Update cart totals with server-calculated values
                        updateCartTotals(response.discount_amount, response.new_total, response.original_total);

                    } else {
                        showCouponError(response.message || 'Invalid coupon code');
                    }
                },
                error: function(xhr) {
                    let errorMessage = 'An error occurred while applying the coupon';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    showCouponError(errorMessage);
                },
                complete: function() {
                    // Reset button state
                    btn.text(originalText).prop('disabled', false);
                }
            });
        });

        // Function to show coupon error
        function showCouponError(message) {
            $('#couponError').text(message).show();
            $('#couponSuccess').hide();
            $('#coupon').addClass('is-invalid');
        }

        // Function to show coupon success
        function showCouponSuccess(message) {
            $('#couponSuccess').text(message).show();
            $('#couponError').hide();
            $('#coupon').removeClass('is-invalid').addClass('is-valid');
        }

        // Function to hide coupon messages
        function hideCouponMessages() {
            $('#couponError, #couponSuccess').hide();
            $('#coupon').removeClass('is-invalid is-valid');
        }

        // Function to update cart totals after coupon application
        function updateCartTotals(discountAmount, newTotal, originalTotal) {
            // Store original total for reset functionality
            if (!window.originalCartTotal) {
                window.originalCartTotal = originalTotal;
            }

            // Add discount row if it doesn't exist
            if ($('.discount-row').length === 0) {
                const discountRow = `
                    <div class="d-flex justify-content-between discount-row">
                        <p class="card-title fs-16 black regular">Discount</p>
                        <p class="fs-16 semi_bold m-0 text-success">-$${discountAmount.toFixed(2)}</p>
                    </div>
                `;
                $('.cart-total').parent().before(discountRow);
            } else {
                $('.discount-row p:last-child').text('-$' + discountAmount.toFixed(2));
            }

            // Update total with server-calculated value
            $('.cart-total').text('$' + newTotal.toFixed(2));

            // Update payment link with discounted total and coupon info
            if (window.appliedCoupon) {
                updatePaymentLink(newTotal, window.appliedCoupon.code, window.appliedCoupon.discount_amount);
            } else {
                updatePaymentLink(newTotal);
            }
        }

        // Function to show applied coupon state
        function showAppliedCouponState(discount) {
            console.log('showAppliedCouponState called with discount:', discount); // Debug log
            console.log('Apply button exists:', $('#applyCouponBtn').length); // Debug log
            console.log('Cross button exists:', $('#removeCouponBtn').length); // Debug log

            // Hide apply button and show cross button
            console.log('Hiding apply button, showing cross button'); // Debug log
            $('#applyCouponBtn').hide();
            $('#removeCouponBtn').removeClass('d-none').addClass('show').show();

            // Update applied coupon display
            // $('#couponSavedAmount').text(`YAY! You saved $${discount.toFixed(2)}`);
            $('#appliedCouponDisplay').removeClass('d-none').show();

            // Disable coupon input
            $('#coupon').prop('readonly', true);

            console.log('Apply button hidden:', $('#applyCouponBtn').is(':hidden')); // Debug log
            console.log('Cross button visible:', $('#removeCouponBtn').is(':visible')); // Debug log
        }

        // Function to update payment link with new total and coupon info
        function updatePaymentLink(newTotal, couponCode = null, discountAmount = null) {
            const paymentLink = $('#goToPaymentStep');
            const baseUrl = paymentLink.data('base-url');
            let newUrl = baseUrl + '?total=' + newTotal.toFixed(2);

            // Add coupon parameters if coupon is applied
            if (couponCode && discountAmount) {
                newUrl += '&coupon_code=' + encodeURIComponent(couponCode);
                newUrl += '&discount_amount=' + discountAmount.toFixed(2);
                newUrl += '&original_total=' + (window.originalCartTotal || newTotal + discountAmount).toFixed(2);
            }

            paymentLink.attr('href', newUrl);
            console.log('Payment link updated to:', newUrl); // Debug log
        }

        // Function to reset payment link to original total
        function resetPaymentLink() {
            const paymentLink = $('#goToPaymentStep');
            const originalTotal = paymentLink.data('original-total');
            const baseUrl = paymentLink.data('base-url');
            const originalUrl = baseUrl + '?total=' + originalTotal;
            paymentLink.attr('href', originalUrl);
            console.log('Payment link reset to:', originalUrl); // Debug log
        }

        // Function to reset coupon state
        function resetCouponState() {
            console.log('resetCouponState called'); // Debug log

            // Show apply button and hide cross button
            $('#applyCouponBtn').show();
            $('#removeCouponBtn').removeClass('show').hide();

            // Hide applied coupon display
            $('#appliedCouponDisplay').hide();

            // Enable coupon input and clear it
            $('#coupon').prop('readonly', false).val('');

            // Hide messages
            hideCouponMessages();

            // Remove discount row and recalculate total
            $('.discount-row').remove();
            recalculateTotal();

            // Reset payment link to original total
            resetPaymentLink();
        }

        // Function to recalculate total without discount
        function recalculateTotal() {
            // Use stored original total if available, otherwise calculate from session
            if (window.originalCartTotal) {
                const originalTotal = window.originalCartTotal;
                $('.cart-total').text('$' + originalTotal.toFixed(2));
                updatePaymentLink(originalTotal); // Update payment link with original total
                window.originalCartTotal = null; // Clear stored value
            } else {
                // Fallback: Make AJAX call to get fresh total from session
                $.ajax({
                    url: "{{ route('get_cart_total') }}",
                    method: 'GET',
                    success: function(response) {
                        if (response.status) {
                            $('.cart-total').text('$' + response.total.toFixed(2));
                            updatePaymentLink(response.total); // Update payment link with fresh total
                        }
                    },
                    error: function() {
                        console.error('Failed to get cart total from server');
                        // Fallback to reset payment link
                        resetPaymentLink();
                    }
                });
            }
        }

        // Handle remove coupon button clicks
        $(document).on('click', '#removeCouponBtn, #removeCouponDisplay', function() {
            console.log('Remove coupon button clicked'); // Debug log
            resetCouponState();
        });

        // Test function - you can call this from browser console: testCouponButtons()
        window.testCouponButtons = function() {
            console.log('Testing coupon buttons...');
            console.log('Apply button exists:', $('#applyCouponBtn').length);
            console.log('Cross button exists:', $('#removeCouponBtn').length);
            console.log('Applied display exists:', $('#appliedCouponDisplay').length);

            // Test showing applied state
            showAppliedCouponState(10);
        };

        // Clear coupon messages when user starts typing
        $(document).on('input', '#coupon', function() {
            if ($(this).val().trim() === '') {
                hideCouponMessages();
            }
        });

        $(document).ready(function() {
            function showStep(stepId) {
                $('.step').removeClass('active');
                $(stepId).addClass('active');
            }

            $('#goToPaymentStep').on('click', function() {
                showStep('#step2');
            });

            $('#goToConfirmation').on('click', function() {
                showStep('#step3');
            });

            $('.back-arrow').on('click', function() {
                let prevStep = $(this).data('prev');
                showStep(prevStep);
            });

        });
        $(document).on('click', '.previous-box .fa-xmark', function() {
            window.location.href = "{{ url('/') }}";
            $('#goToConfirmation').hide();

            $('#terms-condition').on('change', function() {
                if ($(this).is(':checked')) {
                    $('#goToConfirmation').show();
                } else {
                    $('#goToConfirmation').hide();
                }
            });
        });
    </script>
@endpush
